browser_component:
  tab_title: "Test Quotes - Google Drive"
  url: "drive.google.com/drive/folders/1v773cxo-vfGpoJOYt9CYY5YGCpiLe5TL"
  address_bar_focused: false
webpage:
  header:
    - type: image
      label: "Drive logo"
      id: "img_drive_logo"
      bounds: {x: 20, y: 115, width: 80, height: 25}
    - type: input
      label: "Search in Drive"
      id: "input_search_drive"
      bounds: {x: 150, y: 110, width: 400, height: 40}
      value: null
    - type: button
      label: "Filter"
      id: "btn_filter"
      bounds: {x: 560, y: 110, width: 40, height: 40}
    - type: button
      label: "Help"
      id: "btn_help"
      bounds: {x: 870, y: 110, width: 40, height: 40}
    - type: button
      label: "Settings"
      id: "btn_settings"
      bounds: {x: 910, y: 110, width: 40, height: 40}
    - type: image
      label: "User profile M"
      id: "img_user_profile_m"
      bounds: {x: 1150, y: 110, width: 40, height: 40}
  sidebar:
    - type: button
      label: "+ New"
      id: "btn_new"
      bounds: {x: 10, y: 180, width: 100, height: 40}
    - type: navigation
      id: "sidebar_nav"
      bounds: {x: 0, y: 230, width: 230, height: 300}
      children:
        - type: link
          label: "Home"
          id: "nav_home"
          bounds: {x: 10, y: 240, width: 100, height: 30}
        - type: link
          label: "My Drive"
          id: "nav_my_drive"
          bounds: {x: 10, y: 270, width: 100, height: 30}
        - type: link
          label: "Computers"
          id: "nav_computers"
          bounds: {x: 10, y: 300, width: 100, height: 30}
        - type: link
          label: "Shared with me"
          id: "nav_shared_with_me"
          bounds: {x: 10, y: 340, width: 120, height: 30}
          state: active
        - type: link
          label: "Recent"
          id: "nav_recent"
          bounds: {x: 10, y: 370, width: 100, height: 30}
        - type: link
          label: "Starred"
          id: "nav_starred"
          bounds: {x: 10, y: 400, width: 100, height: 30}
        - type: link
          label: "Spam"
          id: "nav_spam"
          bounds: {x: 10, y: 430, width: 100, height: 30}
        - type: link
          label: "Trash"
          id: "nav_trash"
          bounds: {x: 10, y: 460, width: 100, height: 30}
        - type: link
          label: "Storage"
          id: "nav_storage"
          bounds: {x: 10, y: 490, width: 100, height: 30}
    - type: text
      label: "310 MB of 15 GB used"
      id: "text_storage_usage"
      bounds: {x: 10, y: 550, width: 150, height: 20}
    - type: button
      label: "Get more storage"
      id: "btn_get_more_storage"
      bounds: {x: 10, y: 580, width: 150, height: 40}
  main_content:
    - type: navigation
      id: "breadcrumbs"
      bounds: {x: 250, y: 190, width: 450, height: 20}
      children:
        - type: link
          label: "Shared with me"
          id: "breadcrumb_shared_with_me"
          bounds: {x: 250, y: 190, width: 100, height: 20}
        - type: text
          label: ">"
          id: "breadcrumb_separator_1"
          bounds: {x: 355, y: 190, width: 10, height: 20}
        - type: link
          label: "Processing"
          id: "breadcrumb_processing"
          bounds: {x: 370, y: 190, width: 80, height: 20}
        - type: text
          label: ">"
          id: "breadcrumb_separator_2"
          bounds: {x: 455, y: 190, width: 10, height: 20}
        - type: link
          label: "American Integrity"
          id: "breadcrumb_american_integrity"
          bounds: {x: 470, y: 190, width: 120, height: 20}
        - type: text
          label: ">"
          id: "breadcrumb_separator_3"
          bounds: {x: 595, y: 190, width: 10, height: 20}
        - type: text
          label: "Test Quotes"
          id: "breadcrumb_test_quotes"
          bounds: {x: 610, y: 190, width: 80, height: 20}
        - type: icon
          label: "People icon"
          id: "icon_people_test_quotes"
          bounds: {x: 700, y: 190, width: 20, height: 20}
    - type: button
      label: "List view"
      id: "btn_list_view"
      bounds: {x: 960, y: 190, width: 40, height: 30}
      state: active
    - type: button
      label: "Grid view"
      id: "btn_grid_view"
      bounds: {x: 1000, y: 190, width: 40, height: 30}
    - type: button
      label: "Info panel"
      id: "btn_info_panel"
      bounds: {x: 1040, y: 190, width: 40, height: 30}
    - type: dropdown
      label: "Type"
      id: "dropdown_type"
      bounds: {x: 250, y: 230, width: 80, height: 30}
    - type: dropdown
      label: "People"
      id: "dropdown_people"
      bounds: {x: 340, y: 230, width: 80, height: 30}
    - type: dropdown
      label: "Modified"
      id: "dropdown_modified"
      bounds: {x: 430, y: 230, width: 90, height: 30}
    - type: dropdown
      label: "Source"
      id: "dropdown_source"
      bounds: {x: 530, y: 230, width: 80, height: 30}
    - type: table
      id: "table_file_list"
      bounds: {x: 250, y: 270, width: 750, height: 200}
      headers: ["Name", "Owner", "Last modified", "File size"]
      children:
        - type: icon
          label: "Sort by Name"
          id: "icon_sort_name"
          bounds: {x: 330, y: 270, width: 15, height: 15}
        - type: icon
          label: "Sort by Last modified"
          id: "icon_sort_last_modified"
          bounds: {x: 780, y: 270, width: 15, height: 15}
      rows:
        - id: "row_troyer_ho3"
          cells:
            - type: image
              label: "PDF icon"
              id: "icon_pdf_troyer"
              bounds: {x: 250, y: 290, width: 20, height: 20}
            - type: text
              label: "Troyer HO3 AI.pdf"
              id: "text_troyer_ho3"
              bounds: {x: 280, y: 290, width: 120, height: 20}
            - type: icon
              label: "People icon"
              id: "icon_people_troyer"
              bounds: {x: 405, y: 290, width: 20, height: 20}
            - type: text
              label: "me"
              id: "text_owner_troyer"
              bounds: {x: 590, y: 290, width: 20, height: 20}
            - type: text
              label: "4:17 PM"
              id: "text_last_modified_troyer"
              bounds: {x: 690, y: 290, width: 60, height: 20}
            - type: text
              label: "140 KB"
              id: "text_file_size_troyer"
              bounds: {x: 810, y: 290, width: 50, height: 20}
            - type: button
              label: "More actions"
              id: "btn_more_actions_troyer"
              bounds: {x: 960, y: 290, width: 20, height: 20}
        - id: "row_towns_ho3"
          cells:
            - type: image
              label: "PDF icon"
              id: "icon_pdf_towns"
              bounds: {x: 250, y: 320, width: 20, height: 20}
            - type: text
              label: "Towns HO3 AI.pdf"
              id: "text_towns_ho3"
              bounds: {x: 280, y: 320, width: 120, height: 20}
            - type: icon
              label: "People icon"
              id: "icon_people_towns"
              bounds: {x: 405, y: 320, width: 20, height: 20}
            - type: text
              label: "me"
              id: "text_owner_towns"
              bounds: {x: 590, y: 320, width: 20, height: 20}
            - type: text
              label: "3:57 PM"
              id: "text_last_modified_towns"
              bounds: {x: 690, y: 320, width: 60, height: 20}
            - type: text
              label: "139 KB"
              id: "text_file_size_towns"
              bounds: {x: 810, y: 320, width: 50, height: 20}
            - type: button
              label: "More actions"
              id: "btn_more_actions_towns"
              bounds: {x: 960, y: 320, width: 20, height: 20}
        - id: "row_rowen_ho3"
          cells:
            - type: image
              label: "PDF icon"
              id: "icon_pdf_rowen"
              bounds: {x: 250, y: 350, width: 20, height: 20}
            - type: text
              label: "Rowen HO3 AI.pdf"
              id: "text_rowen_ho3"
              bounds: {x: 280, y: 350, width: 120, height: 20}
            - type: icon
              label: "People icon"
              id: "icon_people_rowen"
              bounds: {x: 405, y: 350, width: 20, height: 20}
            - type: text
              label: "me"
              id: "text_owner_rowen"
              bounds: {x: 590, y: 350, width: 20, height: 20}
            - type: text
              label: "4:09 PM"
              id: "text_last_modified_rowen"
              bounds: {x: 690, y: 350, width: 60, height: 20}
            - type: text
              label: "139 KB"
              id: "text_file_size_rowen"
              bounds: {x: 810, y: 350, width: 50, height: 20}
            - type: button
              label: "More actions"
              id: "btn_more_actions_rowen"
              bounds: {x: 960, y: 350, width: 20, height: 20}
        - id: "row_guevara_ho3"
          cells:
            - type: image
              label: "PDF icon"
              id: "icon_pdf_guevara"
              bounds: {x: 250, y: 380, width: 20, height: 20}
            - type: text
              label: "Guevara HO3 AI.pdf"
              id: "text_guevara_ho3"
              bounds: {x: 280, y: 380, width: 120, height: 20}
            - type: icon
              label: "People icon"
              id: "icon_people_guevara"
              bounds: {x: 405, y: 380, width: 20, height: 20}
            - type: text
              label: "me"
              id: "text_owner_guevara"
              bounds: {x: 590, y: 380, width: 20, height: 20}
            - type: text
              label: "4:34 PM"
              id: "text_last_modified_guevara"
              bounds: {x: 690, y: 380, width: 60, height: 20}
            - type: text
              label: "139 KB"
              id: "text_file_size_guevara"
              bounds: {x: 810, y: 380, width: 50, height: 20}
            - type: button
              label: "More actions"
              id: "btn_more_actions_guevara"
              bounds: {x: 960, y: 380, width: 20, height: 20}
        - id: "row_grady_ho3"
          cells:
            - type: image
              label: "PDF icon"
              id: "icon_pdf_grady"
              bounds: {x: 250, y: 410, width: 20, height: 20}
            - type: text
              label: "Grady HO3 AI.pdf"
              id: "text_grady_ho3"
              bounds: {x: 280, y: 410, width: 120, height: 20}
            - type: icon
              label: "People icon"
              id: "icon_people_grady"
              bounds: {x: 405, y: 410, width: 20, height: 20}
            - type: text
              label: "me"
              id: "text_owner_grady"
              bounds: {x: 590, y: 410, width: 20, height: 20}
            - type: text
              label: "4:39 PM"
              id: "text_last_modified_grady"
              bounds: {x: 690, y: 410, width: 60, height: 20}
            - type: text
              label: "139 KB"
              id: "text_file_size_grady"
              bounds: {x: 810, y: 410, width: 50, height: 20}
            - type: button
              label: "More actions"
              id: "btn_more_actions_grady"
              bounds: {x: 960, y: 410, width: 20, height: 20}
        - id: "row_cassidy_ho3"
          cells:
            - type: image
              label: "PDF icon"
              id: "icon_pdf_cassidy"
              bounds: {x: 250, y: 440, width: 20, height: 20}
            - type: text
              label: "Cassidy HO3 AI.pdf"
              id: "text_cassidy_ho3"
              bounds: {x: 280, y: 440, width: 120, height: 20}
            - type: icon
              label: "People icon"
              id: "icon_people_cassidy"
              bounds: {x: 405, y: 440, width: 20, height: 20}
            - type: text
              label: "me"
              id: "text_owner_cassidy"
              bounds: {x: 590, y: 440, width: 20, height: 20}
            - type: text
              label: "4:44 PM"
              id: "text_last_modified_cassidy"
              bounds: {x: 690, y: 440, width: 60, height: 20}
            - type: text
              label: "277 KB"
              id: "text_file_size_cassidy"
              bounds: {x: 810, y: 440, width: 50, height: 20}
            - type: button
              label: "More actions"
              id: "btn_more_actions_cassidy"
              bounds: {x: 960, y: 440, width: 20, height: 20}
  sidebar:
    - type: icon
      label: "Calendar"
      id: "icon_calendar"
      bounds: {x: 1160, y: 290, width: 30, height: 30}
    - type: icon
      label: "Keep"
      id: "icon_keep"
      bounds: {x: 1160, y: 330, width: 30, height: 30}
    - type: icon
      label: "Tasks"
      id: "icon_tasks"
      bounds: {x: 1160, y: 370, width: 30, height: 30}
    - type: icon
      label: "Contacts"
      id: "icon_contacts"
      bounds: {x: 1160, y: 410, width: 30, height: 30}
    - type: button
      label: "Add-ons"
      id: "btn_add_ons"
      bounds: {x: 1160, y: 450, width: 30, height: 30}
    - type: button
      label: "Hide side panel"
      id: "btn_hide_side_panel"
      bounds: {x: 1160, y: 900, width: 30, height: 30}