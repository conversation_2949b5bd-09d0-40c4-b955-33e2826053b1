values_changed:
  root['webpage']['main_content'][0]['subcomponents'][1]['children'][2]['children'][0]['bounds']['y']:
    new_value: 340
    old_value: 360
  root['webpage']['header'][0]['subcomponents'][1]:
    new_value:
      type: navigation
      id: main_nav
      bounds:
        x: 1249
        y: 100
        width: 500
        height: 30
      children:
      - type: link
        label: Home
        id: nav_home
        bounds:
          x: 1250
          y: 105
          width: 50
          height: 20
        state: active
      - type: link
        label: Quote/Policy
        id: nav_quote_policy
        bounds:
          x: 1320
          y: 105
          width: 100
          height: 20
      - type: link
        label: Claims
        id: nav_claims
        bounds:
          x: 1440
          y: 105
          width: 60
          height: 20
      - type: link
        label: Cabinets
        id: nav_cabinets
        bounds:
          x: 1520
          y: 105
          width: 70
          height: 20
      - type: link
        label: Support
        id: nav_support
        bounds:
          x: 1610
          y: 105
          width: 60
          height: 20
      - type: button
        label: '... MORE'
        id: btn_more
        bounds:
          x: 1690
          y: 105
          width: 60
          height: 20
    old_value:
      type: navigation
      id: main_nav
      bounds:
        x: 1249
        y: 89
        width: 500
        height: 30
      children:
      - type: link
        label: Home
        id: nav_home
        bounds:
          x: 1250
          y: 90
          width: 50
          height: 20
        state: active
      - type: link
        label: Quote/Policy
        id: nav_quote_policy
        bounds:
          x: 1320
          y: 90
          width: 100
          height: 20
      - type: link
        label: Claims
        id: nav_claims
        bounds:
          x: 1440
          y: 90
          width: 60
          height: 20
      - type: link
        label: Cabinets
        id: nav_cabinets
        bounds:
          x: 1520
          y: 90
          width: 70
          height: 20
      - type: link
        label: Support
        id: nav_support
        bounds:
          x: 1610
          y: 90
          width: 60
          height: 20
      - type: button
        label: '... MORE'
        id: btn_more
        bounds:
          x: 1690
          y: 90
          width: 60
          height: 20
  root['webpage']['header'][0]['subcomponents'][0]:
    new_value:
      type: image
      label: American Integrity logo
      id: logo_american_integrity
      bounds:
        x: 15
        y: 100
        width: 150
        height: 25
    old_value:
      type: image
      label: American Integrity logo
      id: logo_american_integrity
      bounds:
        x: 15
        y: 80
        width: 150
        height: 40
  root['webpage']['sidebar'][0]:
    new_value:
      component_type: sidebar
      id: left_sidebar
      bounds:
        x: 0
        y: 130
        width: 250
        height: 850
      subcomponents:
      - type: input
        label: null
        id: input_search
        bounds:
          x: 15
          y: 155
          width: 180
          height: 35
        value: Search
      - type: button
        label: null
        id: btn_search
        bounds:
          x: 195
          y: 155
          width: 35
          height: 35
        children:
        - type: icon
          label: Search
          id: icon_search
      - type: text
        label: 'ADVANCED SEARCH:'
        id: text_advanced_search
        bounds:
          x: 15
          y: 200
          width: 120
          height: 20
      - type: link
        label: POLICY
        id: link_policy
        bounds:
          x: 140
          y: 200
          width: 50
          height: 20
      - type: link
        label: CLAIMS
        id: link_claims
        bounds:
          x: 200
          y: 200
          width: 50
          height: 20
      - type: navigation
        id: sidebar_nav
        bounds:
          x: 0
          y: 230
          width: 250
          height: 150
        children:
        - type: link
          label: News
          id: nav_news
          bounds:
            x: 15
            y: 240
            width: 220
            height: 30
          state: active
        - type: link
          label: Inbox
          id: nav_inbox
          bounds:
            x: 15
            y: 270
            width: 220
            height: 30
          children:
          - type: badge
            label: '152'
            id: badge_inbox
            bounds:
              x: 200
              y: 275
              width: 30
              height: 20
        - type: link
          label: Recent List
          id: nav_recent_list
          bounds:
            x: 15
            y: 300
            width: 220
            height: 30
    old_value:
      component_type: sidebar
      id: left_sidebar
      bounds:
        x: 0
        y: 130
        width: 250
        height: 850
      subcomponents:
      - type: input
        label: null
        id: input_search
        bounds:
          x: 15
          y: 145
          width: 180
          height: 35
        value: Search
      - type: button
        label: null
        id: btn_search
        bounds:
          x: 195
          y: 145
          width: 35
          height: 35
        children:
        - type: icon
          label: Search
          id: icon_search
      - type: text
        label: 'ADVANCED SEARCH:'
        id: text_advanced_search
        bounds:
          x: 15
          y: 190
          width: 120
          height: 20
      - type: link
        label: POLICY
        id: link_policy
        bounds:
          x: 140
          y: 190
          width: 50
          height: 20
      - type: link
        label: CLAIMS
        id: link_claims
        bounds:
          x: 200
          y: 190
          width: 50
          height: 20
      - type: navigation
        id: sidebar_nav
        bounds:
          x: 0
          y: 220
          width: 250
          height: 150
        children:
        - type: link
          label: News
          id: nav_news
          bounds:
            x: 15
            y: 230
            width: 220
            height: 30
          state: active
        - type: link
          label: Inbox
          id: nav_inbox
          bounds:
            x: 15
            y: 260
            width: 220
            height: 30
          children:
          - type: badge
            label: '152'
            id: badge_inbox
            bounds:
              x: 200
              y: 265
              width: 30
              height: 20
        - type: link
          label: Recent List
          id: nav_recent_list
          bounds:
            x: 15
            y: 290
            width: 220
            height: 30
  root['webpage']['sidebar'][1]:
    new_value:
      component_type: sidebar
      id: right_floating_sidebar
      bounds:
        x: 1880
        y: 130
        width: 40
        height: 100
      subcomponents:
      - type: button
        label: WTRCRFT QUICK QT
        id: btn_quick_quote
        bounds:
          x: 1885
          y: 150
          width: 30
          height: 40
      - type: button
        label: NEW QUOTE
        id: btn_new_quote
        bounds:
          x: 1885
          y: 200
          width: 30
          height: 40
    old_value:
      component_type: sidebar
      id: right_floating_sidebar
      bounds:
        x: 1880
        y: 130
        width: 40
        height: 100
      subcomponents:
      - type: button
        label: WTRCRFT QUICK QT
        id: btn_quick_quote
        bounds:
          x: 1885
          y: 140
          width: 30
          height: 40
      - type: button
        label: NEW QUOTE
        id: btn_new_quote
        bounds:
          x: 1885
          y: 190
          width: 30
          height: 40
