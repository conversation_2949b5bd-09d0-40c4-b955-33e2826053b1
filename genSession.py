import json
import os
import google.generativeai as genai

# The name of the file where we'll store the chat history
HISTORY_FILE = "chat_history.json"

def load_history(filename=HISTORY_FILE):
    """Loads chat history from a JSON file."""
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8') as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                # Handle cases where the file is empty or corrupted
                return [] 
    return []

def save_history(history, filename=HISTORY_FILE):
    """Saves the chat history to a JSON file."""
    # The 'history' object from the library is not directly JSON-serializable.
    # We need to convert it to a list of dictionaries.
    serializable_history = [
        {"role": msg.role, "parts": [part.text for part in msg.parts]}
        for msg in history
    ]
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(serializable_history, f, indent=2)

def main():
    """Main function to run the chat loop with Google GenAI."""
    # --- Configuration ---
    try:
        # Configure the API key from environment variables
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_API_KEY environment variable not set.")
        genai.configure(api_key=api_key)
    except Exception as e:
        print(f"Error: {e}")
        print("Please make sure you have set your GOOGLE_API_KEY environment variable.")
        return

    # --- Initialization ---
    model = genai.GenerativeModel('gemini-1.5-pro-latest')
    
    # Load previous history
    chat_history_from_file = load_history()
    
    # Start a chat session with the loaded history
    chat = model.start_chat(history=chat_history_from_file)

    model.system_prompt = ""
    
    print("Chat session started. Type 'quit' to exit.")
    if chat_history_from_file:
        print("--- Previous Conversation Loaded ---")

    # --- Chat Loop ---
    while True:
        user_input = input("You: ")
        if user_input.lower() == 'quit':
            print("Exiting and saving history.")
            break

        # Send the message to the model
        response = chat.send_message(user_input)
        
        # Print the model's response
        print(f"Model: {response.text}")
        
        # The chat.history is automatically updated by the library.
        # We just need to save it.
        save_history(chat.history)

if __name__ == "__main__":
    main()