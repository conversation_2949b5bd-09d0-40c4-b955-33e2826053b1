import os
import yaml
import json
import google.generativeai as genai
from pathlib import Path
from datetime import datetime
import time

RESULTS_FILE = "yaml_analysis_results.json"
HISTORY_FILE = "chat_history_context.json"
METADATA_FILE = "metadata_context.json"

class ContextGenerator:
    def __init__(self, api_key="AIzaSyDhnI4SD3TdF-rUCq8UxtbNDOiDA-Np7Pw"):
        """Initialize the Context Generator with API key"""
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.5-flash')  # Adjusted to valid model name; original '2.5' may be a typo
        
        # Enhanced prompt template to incorporate previous context, handle no changes explicitly, and emphasize history tracking
        self.PROMPT_TEMPLATE = """
You are an AI that analyzes sequential YAML deltas representing UI frame changes. Your task is to generate a JSON object describing the current UI state, detect changes from the previous state in the conversation history, and manage a contextId that groups deltas by unique URLs.

ContextId Rules:

You must maintain a persistent map of URLs to contextIds throughout the conversation.

If this is the first delta, assign contextId: 1 to the first URL encountered.

When a new delta arrives:

If the delta has no 'url' field OR its 'url' is the same as the previous one: Keep the contextId and currentUrl the same as the previous state.

If the delta has a 'url' field that is DIFFERENT from the previous state's URL:
a. Search the conversation history: Look for the new URL in all previous states you have analyzed.
b. If the URL has been seen before: Reuse the contextId previously associated with that URL.
c. If the URL is brand new (never seen before in the history): Create a new contextId by finding the highest contextId used so far and adding 1.

Focus on detecting changes in:

Tab switches (via url or browser_component.url/tab_title changes) — this triggers the contextId logic if the URL changes.

Sidebar navigation updates (e.g., active links or sections).

Menu selections (e.g., dropdown or nav menu changes).

Active UI components (e.g., tabs, modals, popups becoming visible/active).

Any other UI deltas provided.

Conversation history contains previous deltas and your JSON outputs. Compare the new delta to the last state in history.


### Contextual Analysis (ENHANCED RULE FOR HISTORY TRACKING):
This analysis is part of an ongoing sequence of YAML diffs or snapshots. The chat history contains all previous YAML inputs (as prompts) and their corresponding analyses (responses). Use this history to build context:
1. Review ALL previous inputs (YAML contents) and responses (analyses) in the chat history to identify patterns, sequences, continuations, or the full track of changes from the first YAML to the current one.
2. Incorporate context from prior analyses, such as ongoing user actions, persistent UI elements, evolving browser states, or the cumulative changes made so far (e.g., "Building on the previous navigation to [url] in the 3rd YAML, and the input change in the 4th YAML, the user now...").
3. Reference relevant previous changes if they impact the current YAML, highlighting the progression or repetitions in UI interactions across the entire sequence (e.g., from 1st to 5th YAML).
4. Maintain continuity in descriptions, avoiding repetition while emphasizing how the current YAML fits into the overall track of changes.
5. If no previous context (first YAML), analyze independently.
6. Explicitly consider the sequence: for example, if this is the 5th YAML, summarize briefly what happened in 1-4 if relevant to the current analysis.

Now analyze the following YAML and produce the summary, using all available context from previous messages in the chat history:
New delta YAML:
{yaml_content}

Output ONLY a valid JSON object (no extra text) with these fields:

"contextId": integer (as per the stateful rules above)

"currentUrl": string (from new delta if present, else from previous; empty if none)

"contextualAnalysis": string (concise description of the current state and any detected changes from previous)
"""

    def is_yaml_empty_or_insignificant(self, yaml_content):
        """
        Check if YAML content is empty, contains only empty objects, or has no meaningful data
        Returns True if the YAML should be skipped
        """
        try:
            # Check if content is empty or just whitespace
            if not yaml_content or not yaml_content.strip():
                return True
            
            # Parse YAML content
            parsed_yaml = yaml.safe_load(yaml_content)
            
            # Check if parsing resulted in None or empty
            if parsed_yaml is None:
                return True
            
            # Check if it's an empty dict, list, or string
            if parsed_yaml == {} or parsed_yaml == [] or parsed_yaml == "":
                return True
            
            # Check if it's a dict with only empty values
            if isinstance(parsed_yaml, dict):
                # Remove None, empty strings, empty dicts, empty lists
                non_empty_values = []
                for value in parsed_yaml.values():
                    if value is not None and value != "" and value != {} and value != []:
                        # For nested dicts, check if they have meaningful content
                        if isinstance(value, dict):
                            if any(v is not None and v != "" and v != {} and v != [] for v in value.values()):
                                non_empty_values.append(value)
                        else:
                            non_empty_values.append(value)
                
                # If no non-empty values found, consider it empty
                if not non_empty_values:
                    return True
            
            # Check if it's a list with only empty elements
            if isinstance(parsed_yaml, list):
                non_empty_items = [item for item in parsed_yaml if item is not None and item != "" and item != {} and item != []]
                if not non_empty_items:
                    return True
            
            return False
            
        except yaml.YAMLError as e:
            # If YAML parsing fails, log the error but don't skip (let LLM handle it)
            print(f"Warning: YAML parsing error (will still process): {str(e)}")
            return False
        except Exception as e:
            print(f"Warning: Error checking YAML content (will still process): {str(e)}")
            return False

    def save_results_to_json(self, results_array, output_file=RESULTS_FILE):
        """Save results array to JSON file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results_array, f, indent=2, ensure_ascii=False)
            print(f"\nResults saved to: {output_file}")
            return output_file
        except Exception as e:
            print(f"Error saving to JSON: {str(e)}")
            return None

    def load_results(self):
        """Load previous results from JSON file"""
        if os.path.exists(RESULTS_FILE):
            with open(RESULTS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []

    def save_history(self, history):
        """Saves the chat history to a JSON file."""
        serializable_history = [
            {"role": message["role"], "parts": [part["text"] for part in message["parts"]]}
            for message in history
        ]
        with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(serializable_history, f, indent=2)
        print("Chat history saved.")

    def load_history(self):
        """Loads the chat history from a JSON file."""
        if os.path.exists(HISTORY_FILE):
            with open(HISTORY_FILE, 'r', encoding='utf-8') as f:
                all_history = json.load(f)
            # Reconstruct the parts as list of dicts
            history = [
                {"role": item["role"], "parts": [{"text": part} for part in item["parts"]]}
                for item in all_history
            ]
            print(f"Chat history loaded: {len(history)} objects.")
            return history
        return []

    def save_metadata(self, metadata):
        """Saves the metadata to a JSON file."""
        with open(METADATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2)

    def load_metadata(self):
        """Loads the metadata from a JSON file."""
        if os.path.exists(METADATA_FILE):
            with open(METADATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

    def get_analysis_strings(self, results_array):
        """Extract just the AI analysis strings from results"""
        return [result['ai_analysis'] for result in results_array]

    def get_file_names(self, results_array):
        """Extract just the file names from results"""
        return [result['file_details']['file_name'] for result in results_array]

    def get_analysis_by_filename(self, results_array):
        """Get dictionary mapping filenames to their analysis"""
        return {result['file_details']['file_name']: result['ai_analysis'] for result in results_array}

    def process_yaml_files(self, folder_path):
        """Process all YAML files in the given folder using a chat session for context"""
        results_array = self.load_results()
        skipped_files = []

        # Get all YAML files from the folder, sorted by name for sequential processing
        yaml_files = sorted(list(Path(folder_path).glob("*.yaml")) + list(Path(folder_path).glob("*.yml")))

        # Load metadata
        metadata = self.load_metadata()

        # Initialize chat history
        history = self.load_history()

        # Start chat session with existing history
        chat = self.model.start_chat(history=history)

        for yaml_file in yaml_files:
            if yaml_file.name in metadata:
                print(f"Skipped (already processed): {yaml_file.name}")
                continue

            try:
                # Read YAML file with fallback encoding
                try:
                    with open(yaml_file, 'r', encoding='utf-8') as file:
                        yaml_content = file.read()
                except UnicodeDecodeError:
                    print(f"UTF-8 decoding failed for {yaml_file.name}, trying cp1252...")
                    with open(yaml_file, 'r', encoding='cp1252') as file:
                        yaml_content = file.read()

                # Check if YAML content is empty or insignificant
                if self.is_yaml_empty_or_insignificant(yaml_content):
                    print(f"Skipped (empty/insignificant): {yaml_file.name}")
                    skipped_files.append(yaml_file.name)
                    continue

                # Create prompt with YAML content
                prompt = self.PROMPT_TEMPLATE.format(yaml_content=yaml_content)

                # Retry logic for sending message
                retries = 0
                max_retries = 4
                while retries < max_retries:
                    try:
                        response = chat.send_message(prompt)
                        break
                    except Exception as e:
                        retries += 1
                        print(f"Attempt {retries}/{max_retries} failed for {yaml_file.name}. Error: {str(e)}")
                        if retries < max_retries:
                            wait_time = 5 ** retries
                            print(f"Retrying in {wait_time} seconds...")
                            time.sleep(wait_time)
                        else:
                            raise Exception(f"Failed to process {yaml_file.name} after {max_retries} attempts.")

                # Append user input and model output to history
                history.append({"role": "user", "parts": [{"text": prompt}]})

                raw_text = response.text.strip()
                print("raw_text",raw_text)
                # Remove Markdown fences if present
                if raw_text.startswith("```"):
                    raw_text = raw_text.split("```")[1]  # get the middle part
                    if raw_text.strip().startswith("json"):
                        raw_text = raw_text.strip()[4:]  # remove 'json' after ```
                    raw_text = raw_text.strip()
                # Validate that the response is valid JSON before writing
                json_data = json.loads(raw_text)
        
                history.append({"role": "model", "parts": [{"text": json.dumps(json_data)}]})


                # Create result object
                result_obj = {
                    "file_details": {
                        "file_name": yaml_file.name,
                        "file_path": str(yaml_file),
                        # "yaml_content": yaml_content
                    },
                    "json_data" : json_data
                    # "chat_history": history[:]  # store a snapshot of history till this file
                }

                results_array.append(result_obj)
                metadata[yaml_file.name] = yaml_file.name
                self.save_results_to_json(results_array)
                self.save_history(history)
                self.save_metadata(metadata)
                print(f"Processed: {yaml_file.name}")

            except Exception as e:
                print(f"Error processing {yaml_file}: {str(e)}")
                raise  # Raise to stop processing on error

        # Print summary of processing
        if skipped_files:
            print(f"\nSummary: Processed {len(results_array)} files, skipped {len(skipped_files)} empty/insignificant files:")
            for skipped in skipped_files:
                print(f"  - {skipped}")

        return results_array

    async def main(self, folder_path, isIntent=None):
        """
        Main processing function that processes YAML files and triggers intent analysis
        Returns the final JSON result from intent generator
        """
        # print(f"Processing folder: {folder_path}")

        # Verify folder exists
        if not os.path.exists(folder_path):
            print(f"Error: Folder does not exist: {folder_path}")
            return None

        # Process all YAML files
        results = self.process_yaml_files(folder_path)

        # Save results to JSON file
        json_file = self.save_results_to_json(results)
        # json_file = r"E:\loveable_AI\bunch\yaml_analysis_results_20250810_224933.json"

        # Trigger intent generator and capture result
        if json_file:
            print(f"\n{'='*50}")
            print("AUTOMATICALLY TRIGGERING INTENT ANALYSIS...")
            print(f"{'='*50}")

            try:
                # Import and use intent generator as a class
                # from intent_generator import IntentGenerator

                # intent_gen = IntentGenerator()
                # intent_result = intent_gen.analyze_video_frames_sync(json_file , isIntent)

                from parameterizationIntent import main

                intent_result = await main(json_file , isIntent)
                print("intent_result", intent_result)
                if intent_result:
                    print("FINAL RESULT FROM INTENT GENERATOR:")
                    print(json.dumps(intent_result, indent=2))
                    return intent_result
                else:
                    print("Intent generator returned no result")
                    return {"status": False, "error_stage": "intent_generator", "error_message": "No result returned by intent generator"}

            except Exception as e:
                print(f"Error running intent_generator: {e}")
                import traceback
                traceback.print_exc()
                return {"status": False, "error_stage": "intent_generator", "error_message": str(e)}

        return {"status": False, "error_stage": "context_generator", "error_message": "No JSON file produced"}

# Backward compatibility for standalone usage

# Main execution
import asyncio

if __name__ == "__main__":
    async def run_main():
        context_gen = ContextGenerator()
        result = await context_gen.main(r"E:\loveable_AI\bunch\ui_element_extraction_20250826_114414\diff_folder", "active")
        if result:
            print("\nProcess completed successfully!")
        else:
            print("\nProcess failed!")

    asyncio.run(run_main())