import json
import google.generativeai as genai
import sys
import os
import asyncio
from agent import PlaywrightAgent  
from pathlib import Path
from typing import List
import PIL.Image


async def analyze_video_frames(json_filename: str):
    """
    Analyze video frames by reading JSON file and passing content to PlaywrightAgent
    
    Args:
        json_filename (str): Name of the JSON file to read
    """
    
    # Check if JSON filename is provided
    if not json_filename:
        print("❌ Error: JSON filename not provided")
        return None
    
    # Construct the full path to the JSON file
    json_file_path = Path(__file__).parent / json_filename
    
    # Check if the JSON file exists
    if not json_file_path.exists():
        print(f"❌ Error: JSON file '{json_filename}' not found in {json_file_path.parent}")
        return None
    
    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as file:
            json_content = json.load(file)
        
        print(f"✅ Successfully loaded JSON file: {json_filename}")
        
        # Create PlaywrightAgent instance
        agent = PlaywrightAgent()
        
        # Convert JSON content to string for the agent
        json_content_str = json.dumps(json_content, indent=2)
        
        # Create instruction for the agent with the JSON content
        user_instruction = f"""
        Analyze the following JSON data which contains browser automation steps:
        
        {json_content_str}
        
        Based on this JSON data, execute the browser automation sequence described in the steps.
        """
        
        # Invoke the agent with the JSON content
        finalresp = await agent.invoke_user_input(user_instruction)
        
        print("🤖 PlaywrightAgent Response:")
        print(finalresp)
        
        return finalresp
        
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid JSON format in file '{json_filename}': {e}")
        return None
    except Exception as e:
        print(f"❌ Error reading or processing file '{json_filename}': {e}")
        return None


def main():
    """Main function to handle command line arguments and run the analysis"""
    
    # Check if JSON filename is provided as command line argument
    if len(sys.argv) != 2:
        print("❌ Usage: python fg.py <json_filename>")
        print("   Example: python fg.py cargarge.json")
        sys.exit(1)
    
    json_filename = sys.argv[1]
    
    # Run the async function
    result = asyncio.run(analyze_video_frames(json_filename))
    
    if result:
        print("✅ Analysis completed successfully!")
    else:
        print("❌ Analysis failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()