import cv2

def reduce_image_size(image_path, output_path, scale_factor=0.5):
    """
    Reduce image size by a scale factor
    
    Args:
        image_path (str): Path to input image
        output_path (str): Path to save resized image
        scale_factor (float): Scale factor (0.5 = 50% of original size)
    """
    # Read the image
    image = cv2.imread(image_path)
    
    if image is None:
        print(f"Error: Could not load image from {image_path}")
        return False
    
    # Get original dimensions
    height, width = image.shape[:2]
    
    # Calculate new dimensions
    new_width = int(width * scale_factor)
    new_height = int(height * scale_factor)
    
    # Resize the image
    resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    # Save the resized image
    cv2.imwrite(output_path, resized_image)
    
    print(f"Image resized from {width}x{height} to {new_width}x{new_height}")
    print(f"Saved to: {output_path}")
    return True

def reduce_image_size_by_dimensions(image_path, output_path, target_width, target_height):
    """
    Resize image to specific dimensions
    
    Args:
        image_path (str): Path to input image
        output_path (str): Path to save resized image
        target_width (int): Target width in pixels
        target_height (int): Target height in pixels
    """
    # Read the image
    image = cv2.imread(image_path)
    
    if image is None:
        print(f"Error: Could not load image from {image_path}")
        return False
    
    # Resize to specific dimensions
    resized_image = cv2.resize(image, (target_width, target_height), interpolation=cv2.INTER_AREA)
    
    # Save the resized image
    cv2.imwrite(output_path, resized_image)
    
    print(f"Image resized to {target_width}x{target_height}")
    print(f"Saved to: {output_path}")
    return True

# Example usage
if __name__ == "__main__":
    # Method 1: Scale by factor
    reduce_image_size(r"E:\loveable_AI\bunch\finalFrames_20250827_110718\frame_0058.jpg", "output_small.jpg", scale_factor=0.75)
    
    # Method 2: Resize to specific dimensions
    # reduce_image_size_by_dimensions("input.jpg", "output_800x600.jpg", 800, 600)