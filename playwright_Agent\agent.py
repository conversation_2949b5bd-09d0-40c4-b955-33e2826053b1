# playwright_runner.py

import asyncio
import sys
import os
from google.adk.agents.llm_agent import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.mcp_tool.mcp_toolset import (
    MCPToolset,
    StdioConnectionParams,
    StdioServerParameters,
)
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from google.adk.sessions import InMemorySessionService
from google.adk.runners import Runner
from google.genai import types
from pydantic import BaseModel, Field
from google.genai.types import Schema
from google.adk.models.lite_llm import LiteLlm

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
# from custom_adk_patches import CustomMCPToolset

# class PlaywrightOutput(BaseModel):
#     page_title: str = Field(description="The title of the page")
#     screenshot_url: str = Field(description="URL to screenshot or image")
#     extracted_text: str = Field(description="Text you extracted from the page")

class PlaywrightAgent:
    def __init__(self, model="gemini-2.0-flash"):
        # self.model = model
        self.model = LiteLlm(model="openai/gpt-5")
        self.agent = self._create_agent()
        self.session_service = InMemorySessionService()
        self.runner = Runner(agent=self.agent, app_name="playwright_app", session_service=self.session_service)
       
    def _create_agent(self):
        return Agent(
            name="playwright_agent",
            model=self.model,
            description="Browser automation agent with Playwright",
            instruction="""
            You are a browser automation agent powered by Playwright. Your job is to interact with web pages just like a human would, using Playwright commands. You can open URLs, click buttons, fill input fields, take screenshots, wait for elements, and extract content.
            """,
            tools=[
                MCPToolset(
                connection_params=StdioConnectionParams(
                        server_params=StdioServerParameters(
                        command="npx",
                        args=["@playwright/mcp@latest"]
                    ),
                    timeout =600.0
                 )             
                )
            ],
        )

    async def invoke_user_input(self, user_input: str):
        await self.session_service.create_session(app_name="playwright_app", user_id="user1", session_id="sess1")
        content = types.Content(parts=[types.Part(text=user_input)], role="user")
        events_async = self.runner.run_async(
            user_id="user1", session_id="sess1", new_message=content
        )

        try:
            async for event in events_async:
                if event.is_final_response() and event.content.parts:
                    return {
                        "status": True,
                        "message": "data retrieved successfully",
                        "data": event.content.parts[0].text
                    }
        except Exception as e:
            print("⚠️ Tool error occurred:", e)
            return {
                "status": False,
                "message": "Failed when using MCP",
                "data": str(e)
            }
        finally:
            await events_async.aclose()

# # Optional local test
if __name__ == "__main__":
    user_instruction = (
f"""{
   {
  "steps": [
    {
      "step_number": 1,
      "action": "Opened new tab and navigated to https://docs.google.com/spreadsheets/d/11nVcxP8BmoKjKcM495sLhM_HmI0atSWu-bX7CYaQ1Zw/edit?usp=sharing",
      "details": {
        "target_element": "browser_tab",

        "page_url": "https://docs.google.com/spreadsheets/d/11nVcxP8BmoKjKcM495sLhM_HmI0atSWu-bX7CYaQ1Zw/edit?usp=sharing"
      }
    },
    {
      "step_number": 2,
      "action": "Switched to tab: AMC-Admin",
      "details": {
        "target_element": "browser_tab",

        "page_url": "http://13.127.87.229/createbrand"
      }
    },
    {
      "step_number": 3,
      "action": "Navigated to http://13.127.87.229/createbrand",
      "details": {
        "target_element": "browser_tab",

        "page_url": "http://13.127.87.229/createbrand"
      }
    },
    {
      "step_number": 4,
      "action": "Entered 'Hisense' in Brand Name input field",
      "details": {
        "target_element": "input_brand_name",
        "input_value": "TCL",
        "page_url": "http://13.127.87.229/createbrand"
      }
    },
    {
      "step_number": 5,
      "action": "Selected 'Active' from Status dropdown",
      "details": {
        "target_element": "dropdown_status",
        "input_value": "Active",
        "page_url": "http://13.127.87.229/createbrand"
      }
    },
    {
      "step_number": 6,
      "action": "Clicked Create button",
      "details": {
        "target_element": "btn_create",

        "page_url": "http://13.127.87.229/createbrand"
      }
    },
    {
      "step_number": 7,
      "action": "Switched to tab: Brand List - Google Sheets",
      "details": {
        "target_element": "browser_tab",

        "page_url": "https://docs.google.com/spreadsheets/d/11nVcxP8BmoKjKcM495sLhM_HmI0atSWu-bX7CYaQ1Zw/edit?usp=sharing"
      }
    }
  ]
}
   }

"""
# "navigate to google.com and search for playwirght"
    )

    asyncio.run(PlaywrightAgent().invoke_user_input(user_instruction))
