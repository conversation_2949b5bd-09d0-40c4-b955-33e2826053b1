import os
import google.generativeai as genai
import yaml  # For parsing YAML if needed
from pathlib import Path
import json  # <-- 1. IMPORT THE JSON LIBRARY
from datetime import datetime

# --- Configuration ---
# Configure API
# It's recommended to use environment variables for API keys for better security
# For example: genai.configure(api_key=os.environ["GEMINI_API_KEY"])
genai.configure(api_key='AIzaSyBTdGX8zJNaCPU8YgaEE_OQJHhzbeg2Wd8')


timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
# FOLDER PATHS
INPUT_FOLDER_PATH = r"E:\loveable_AI\bunch\helper_funtion_codes\helper_diff" # <- Your input folder
OUTPUT_FOLDER_PATH = rf"E:\loveable_AI\bunch\helper_funtion_codes\json_outputs_{timestamp}"

Path(OUTPUT_FOLDER_PATH).mkdir(parents=True, exist_ok=True)

# Initialize model
model = genai.GenerativeModel('gemini-1.5-flash')

# Start chat session with empty history
chat = model.start_chat(history=[])

# Example prompt template
PROMPT_TEMPLATE = """
You are an AI that analyzes sequential YAML deltas representing UI frame changes. Your task is to generate a JSON object describing the current UI state, detect changes from the previous state in the conversation history, and manage a contextId that groups deltas by unique URLs.

ContextId Rules:

You must maintain a persistent map of URLs to contextIds throughout the conversation.

If this is the first delta, assign contextId: 1 to the first URL encountered.

When a new delta arrives:

If the delta has no 'url' field OR its 'url' is the same as the previous one: Keep the contextId and currentUrl the same as the previous state.

If the delta has a 'url' field that is DIFFERENT from the previous state's URL:
a. Search the conversation history: Look for the new URL in all previous states you have analyzed.
b. If the URL has been seen before: Reuse the contextId previously associated with that URL.
c. If the URL is brand new (never seen before in the history): Create a new contextId by finding the highest contextId used so far and adding 1.

Focus on detecting changes in:

Tab switches (via url or browser_component.url/tab_title changes) — this triggers the contextId logic if the URL changes.

Sidebar navigation updates (e.g., active links or sections).

Menu selections (e.g., dropdown or nav menu changes).

Active UI components (e.g., tabs, modals, popups becoming visible/active).

Any other UI deltas provided.

Conversation history contains previous deltas and your JSON outputs. Compare the new delta to the last state in history.


### Contextual Analysis (ENHANCED RULE FOR HISTORY TRACKING):
This analysis is part of an ongoing sequence of YAML diffs or snapshots. The chat history contains all previous YAML inputs (as prompts) and their corresponding analyses (responses). Use this history to build context:
1. Review ALL previous inputs (YAML contents) and responses (analyses) in the chat history to identify patterns, sequences, continuations, or the full track of changes from the first YAML to the current one.
2. Incorporate context from prior analyses, such as ongoing user actions, persistent UI elements, evolving browser states, or the cumulative changes made so far (e.g., "Building on the previous navigation to [url] in the 3rd YAML, and the input change in the 4th YAML, the user now...").
3. Reference relevant previous changes if they impact the current YAML, highlighting the progression or repetitions in UI interactions across the entire sequence (e.g., from 1st to 5th YAML).
4. Maintain continuity in descriptions, avoiding repetition while emphasizing how the current YAML fits into the overall track of changes.
5. If no previous context (first YAML), analyze independently.
6. Explicitly consider the sequence: for example, if this is the 5th YAML, summarize briefly what happened in 1-4 if relevant to the current analysis.

Now analyze the following YAML and produce the summary, using all available context from previous messages in the chat history:
New delta YAML:
{yaml_delta}

Output ONLY a valid JSON object (no extra text) with these fields:

"contextId": integer (as per the stateful rules above)

"currentUrl": string (from new delta if present, else from previous; empty if none)

"contextualAnalysis": string (concise description of the current state and any detected changes from previous)
"""

# ---- Load YAML deltas from folder ----
# Collect only .yaml / .yml files, sorted by name (so sequence is consistent)
yaml_files = sorted(
    [f for f in Path(INPUT_FOLDER_PATH).iterdir() if f.suffix in [".yaml", ".yml"]]
)

# Process each YAML file sequentially
for i, file_path in enumerate(yaml_files, start=1):
    with open(file_path, "r", encoding="utf-8") as f:
        yaml_str = f.read()
    
    prompt = PROMPT_TEMPLATE.format(yaml_delta=yaml_str)

    # Send to chat (history is preserved automatically)
    response = chat.send_message(prompt)

    # --- 4. MODIFIED SECTION TO SAVE THE RESPONSE ---
    print(f"Processing Delta ({file_path.name})...")

    # Construct the output filename based on the input filename
    # e.g., 'delta_01.yaml' becomes 'delta_01.json'
    output_filename = file_path.with_suffix(".json").name
    output_filepath = Path(OUTPUT_FOLDER_PATH) / output_filename
    
    try:

        raw_text = response.text.strip()

        # Remove Markdown fences if present
        if raw_text.startswith("```"):
            raw_text = raw_text.split("```")[1]  # get the middle part
            if raw_text.strip().startswith("json"):
                raw_text = raw_text.strip()[4:]  # remove 'json' after ```
            raw_text = raw_text.strip()
        # Validate that the response is valid JSON before writing
        json_data = json.loads(raw_text)
        
        # Write the JSON data to the file with nice formatting (indent=4)
        with open(output_filepath, "w", encoding="utf-8") as json_file:
            json.dump(json_data, json_file, indent=4)
            
        print(f"✅ Successfully saved response to '{output_filepath}'")

    except json.JSONDecodeError:
        # Handle cases where the AI response isn't valid JSON
        print(f"❌ Error: Could not parse the AI response for '{file_path.name}' as JSON.")
        print("--- AI Response Start ---")
        print(json_data)
        print("--- AI Response End ---")
    
    print("-" * 50)