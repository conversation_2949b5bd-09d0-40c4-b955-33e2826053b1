[{"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: Brand List - Google Sheets\n  url: docs.google.com/spreadsheets/d/11nVoXP8BmoKjKcM495sLhM_Hml0atSWu-bX7CYaQ1Zw/edit?gid=0#gid=0\n  address_bar_focused: false\nwebpage:\n  header:\n    - type: container\n      id: header_main\n      bounds: {x: 0, y: 70, width: 1920, height: 88}\n      children:\n        - type: image\n          id: sheets_logo\n          label: Sheets home\n          bounds: {x: 12, y: 80, width: 40, height: 40}\n        - type: container\n          id: document_header\n          bounds: {x: 60, y: 80, width: 1860, height: 80}\n          children:\n            - type: text\n              id: doc_title\n              label: Brand List\n              bounds: {x: 70, y: 85, width: 100, height: 20}\n            - type: button\n              id: btn_star\n              label: null\n              bounds: {x: 175, y: 85, width: 24, height: 24}\n            - type: button\n              id: btn_move\n              label: null\n              bounds: {x: 205, y: 85, width: 24, height: 24}\n            - type: button\n              id: btn_cloud_status\n              label: null\n              bounds: {x: 235, y: 85, width: 24, height: 24}\n            - type: navigation\n              id: main_menu\n              bounds: {x: 70, y: 115, width: 500, height: 30}\n              children:\n                - type: link\n                  id: menu_file\n                  label: File\n                - type: link\n                  id: menu_edit\n                  label: Edit\n                - type: link\n                  id: menu_view\n                  label: View\n                - type: link\n                  id: menu_insert\n                  label: Insert\n                - type: link\n                  id: menu_format\n                  label: Format\n                - type: link\n                  id: menu_data\n                  label: Data\n                - type: link\n                  id: menu_tools\n                  label: Tools\n                - type: link\n                  id: menu_extensions\n                  label: Extensions\n                - type: link\n                  id: menu_help\n                  label: Help\n            - type: button\n              id: btn_history\n              label: null\n              bounds: {x: 1550, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_comments\n              label: null\n              bounds: {x: 1590, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_meet\n              label: null\n              bounds: {x: 1630, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_share\n              label: Share\n              bounds: {x: 1680, y: 85, width: 90, height: 36}\n            - type: image\n              id: user_avatar\n              label: null\n              bounds: {x: 1790, y: 85, width: 32, height: 32}\n  main_content:\n    - type: container\n      id: toolbar_and_grid\n      bounds: {x: 0, y: 158, width: 1850, height: 850}\n      children:\n        - type: container\n          id: formula_bar\n          bounds: {x: 0, y: 200, width: 1850, height: 30}\n          children:\n            - type: input\n              id: input_cell_selector\n              label: null\n              value: B9\n              bounds: {x: 10, y: 205, width: 50, height: 25}\n            - type: input\n              id: input_formula\n              label: null\n              value: Hisense\n              bounds: {x: 70, y: 205, width: 1780, height: 25}\n        - type: table\n          id: spreadsheet_grid\n          bounds: {x: 0, y: 235, width: 1850, height: 770}\n          headers: [A, B, C, D, E, F, G, H, I, J, K, L, M, N, O]\n          rows:\n            - id: row_1\n              cells:\n                - type: text\n                  id: cell_A1\n                  label: '#'\n                - type: text\n                  id: cell_B1\n                  label: Brand\n            - id: row_2\n              cells:\n                - type: text\n                  id: cell_A2\n                  label: '1'\n                - type: text\n                  id: cell_B2\n                  label: VStar\n            - id: row_3\n              cells:\n                - type: text\n                  id: cell_A3\n                  label: '2'\n                - type: text\n                  id: cell_B3\n                  label: BPL\n            - id: row_4\n              cells:\n                - type: text\n                  id: cell_A4\n                  label: '3'\n                - type: text\n                  id: cell_B4\n                  label: Godrej\n            - id: row_5\n              cells:\n                - type: text\n                  id: cell_A5\n                  label: '4'\n                - type: text\n                  id: cell_B5\n                  label: Intex\n            - id: row_6\n              cells:\n                - type: text\n                  id: cell_A6\n                  label: '5'\n                - type: text\n                  id: cell_B6\n                  label: Lloyd\n            - id: row_7\n              cells:\n                - type: text\n                  id: cell_A7\n                  label: '6'\n                - type: text\n                  id: cell_B7\n                  label: Lloyd\n            - id: row_8\n              cells:\n                - type: text\n                  id: cell_A8\n                  label: '7'\n                - type: text\n                  id: cell_B8\n                  label: IFB\n            - id: row_9\n              cells:\n                - type: text\n                  id: cell_A9\n                  label: '8'\n                - type: text\n                  id: cell_B9\n                  label: Hisense\n                  state: selected\n  sidebar:\n    - type: container\n      id: right_sidebar\n      bounds: {x: 1870, y: 160, width: 50, height: 850}\n      children:\n        - type: button\n          id: btn_calendar\n          label: Calendar\n          bounds: {x: 1880, y: 260, width: 30, height: 30}\n        - type: button\n          id: btn_keep\n          label: Keep\n          bounds: {x: 1880, y: 310, width: 30, height: 30}\n        - type: button\n          id: btn_tasks\n          label: Tasks\n          bounds: {x: 1880, y: 360, width: 30, height: 30}\n        - type: button\n          id: btn_contacts\n          label: Contacts\n          bounds: {x: 1880, y: 410, width: 30, height: 30}\n        - type: button\n          id: btn_maps\n          label: Maps\n          bounds: {x: 1880, y: 460, width: 30, height: 30}\n        - type: button\n          id: btn_add_ons\n          label: Get Add-ons\n          bounds: {x: 1880, y: 530, width: 30, height: 30}\n  footer:\n    - type: container\n      id: sheet_navigation\n      bounds: {x: 0, y: 980, width: 1850, height: 30}\n      children:\n        - type: button\n          id: btn_add_sheet\n          label: null\n          bounds: {x: 10, y: 985, width: 24, height: 24}\n        - type: button\n          id: btn_all_sheets\n          label: null\n          bounds: {x: 40, y: 985, width: 24, height: 24}\n        - type: button\n          id: btn_sheet1\n          label: Sheet1\n          bounds: {x: 70, y: 982, width: 80, height: 28}\n          state: active\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: Brand List - Google Sheets\n  url: docs.google.com/spreadsheets/d/11nVoXP8BmoKjKcM495sLhM_Hml0atSWu-bX7CYaQ1Zw/edit?gid=0#gid=0\n  address_bar_focused: false\nwebpage:\n  header:\n    - type: container\n      id: header_main\n      bounds: {x: 0, y: 70, width: 1920, height: 118}\n      children:\n        - type: image\n          id: sheets_logo\n          label: Sheets home\n          bounds: {x: 12, y: 80, width: 40, height: 40}\n        - type: container\n          id: document_header\n          bounds: {x: 60, y: 80, width: 1860, height: 40}\n          children:\n            - type: text\n              id: doc_title\n              label: Brand List\n              bounds: {x: 70, y: 85, width: 100, height: 20}\n            - type: button\n              id: btn_star\n              label: null\n              bounds: {x: 175, y: 85, width: 24, height: 24}\n            - type: button\n              id: btn_move\n              label: null\n              bounds: {x: 205, y: 85, width: 24, height: 24}\n            - type: button\n              id: btn_cloud_status\n              label: null\n              bounds: {x: 235, y: 85, width: 24, height: 24}\n            - type: button\n              id: btn_history\n              label: null\n              bounds: {x: 1290, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_comments\n              label: null\n              bounds: {x: 1330, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_meet\n              label: null\n              bounds: {x: 1370, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_share\n              label: Share\n              bounds: {x: 1420, y: 85, width: 90, height: 36}\n            - type: image\n              id: user_avatar\n              label: null\n              bounds: {x: 1530, y: 85, width: 32, height: 32}\n        - type: navigation\n          id: main_menu\n          bounds: {x: 70, y: 115, width: 500, height: 30}\n          children:\n            - type: link\n              id: menu_file\n              label: File\n            - type: link\n              id: menu_edit\n              label: Edit\n            - type: link\n              id: menu_view\n              label: View\n            - type: link\n              id: menu_insert\n              label: Insert\n            - type: link\n              id: menu_format\n              label: Format\n            - type: link\n              id: menu_data\n              label: Data\n            - type: link\n              id: menu_tools\n              label: Tools\n            - type: link\n              id: menu_extensions\n              label: Extensions\n            - type: link\n              id: menu_help\n              label: Help\n        - type: container\n          id: toolbar\n          bounds: {x: 0, y: 148, width: 1920, height: 40}\n          children:\n            - type: button\n              id: btn_undo\n              label: null\n            - type: button\n              id: btn_redo\n              label: null\n            - type: button\n              id: btn_print\n              label: null\n            - type: button\n              id: btn_paint_format\n              label: null\n            - type: dropdown\n              id: dropdown_zoom\n              label: 100%\n            - type: dropdown\n              id: dropdown_format\n              label: 123\n            - type: dropdown\n              id: dropdown_font\n              label: Default\n            - type: dropdown\n              id: dropdown_font_size\n              label: '10'\n            - type: button\n              id: btn_bold\n              label: null\n            - type: button\n              id: btn_italic\n              label: null\n            - type: button\n              id: btn_strikethrough\n              label: null\n            - type: button\n              id: btn_text_color\n              label: null\n  main_content:\n    - type: container\n      id: spreadsheet_area\n      bounds: {x: 0, y: 188, width: 1850, height: 770}\n      children:\n        - type: container\n          id: formula_bar\n          bounds: {x: 0, y: 188, width: 1850, height: 30}\n          children:\n            - type: input\n              id: input_cell_selector\n              label: null\n              value: B9\n              bounds: {x: 10, y: 193, width: 50, height: 25}\n            - type: input\n              id: input_formula\n              label: null\n              value: Hisense\n              bounds: {x: 70, y: 193, width: 1780, height: 25}\n        - type: table\n          id: spreadsheet_grid\n          bounds: {x: 0, y: 223, width: 1850, height: 735}\n          headers: [A, B, C, D, E, F, G, H, I, J, K, L, M, N, O]\n          rows:\n            - id: row_1\n              cells:\n                - type: text\n                  id: cell_A1\n                  label: '#'\n                - type: text\n                  id: cell_B1\n                  label: Brand\n            - id: row_2\n              cells:\n                - type: text\n                  id: cell_A2\n                  label: '1'\n                - type: text\n                  id: cell_B2\n                  label: VStar\n            - id: row_3\n              cells:\n                - type: text\n                  id: cell_A3\n                  label: '2'\n                - type: text\n                  id: cell_B3\n                  label: BPL\n            - id: row_4\n              cells:\n                - type: text\n                  id: cell_A4\n                  label: '3'\n                - type: text\n                  id: cell_B4\n                  label: Godrej\n            - id: row_5\n              cells:\n                - type: text\n                  id: cell_A5\n                  label: '4'\n                - type: text\n                  id: cell_B5\n                  label: Intex\n            - id: row_6\n              cells:\n                - type: text\n                  id: cell_A6\n                  label: '5'\n                - type: text\n                  id: cell_B6\n                  label: Lloyd\n            - id: row_7\n              cells:\n                - type: text\n                  id: cell_A7\n                  label: '6'\n                - type: text\n                  id: cell_B7\n                  label: Lloyd\n            - id: row_8\n              cells:\n                - type: text\n                  id: cell_A8\n                  label: '7'\n                - type: text\n                  id: cell_B8\n                  label: IFB\n            - id: row_9\n              cells:\n                - type: text\n                  id: cell_A9\n                  label: '8'\n                - type: text\n                  id: cell_B9\n                  label: Hisense\n                  state: selected\n  sidebar:\n    - type: container\n      id: right_sidebar\n      bounds: {x: 1870, y: 223, width: 50, height: 735}\n      children:\n        - type: button\n          id: btn_calendar\n          label: null\n          bounds: {x: 1880, y: 260, width: 30, height: 30}\n        - type: button\n          id: btn_keep\n          label: null\n          bounds: {x: 1880, y: 310, width: 30, height: 30}\n        - type: button\n          id: btn_tasks\n          label: null\n          bounds: {x: 1880, y: 360, width: 30, height: 30}\n        - type: button\n          id: btn_contacts\n          label: null\n          bounds: {x: 1880, y: 410, width: 30, height: 30}\n        - type: button\n          id: btn_maps\n          label: null\n          bounds: {x: 1880, y: 460, width: 30, height: 30}\n        - type: button\n          id: btn_add_ons\n          label: null\n          bounds: {x: 1880, y: 530, width: 30, height: 30}\n  footer:\n    - type: container\n      id: sheet_navigation\n      bounds: {x: 0, y: 958, width: 1850, height: 30}\n      children:\n        - type: button\n          id: btn_add_sheet\n          label: null\n          bounds: {x: 10, y: 963, width: 24, height: 24}\n        - type: button\n          id: btn_all_sheets\n          label: null\n          bounds: {x: 40, y: 963, width: 24, height: 24}\n        - type: button\n          id: btn_sheet1\n          label: Sheet1\n          bounds: {x: 70, y: 960, width: 80, height: 28}\n          state: active\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: Brand List - Google Sheets\n  url: docs.google.com/spreadsheets/d/11nVoXP8BmoKjKcM495sLhM_Hml0atSWu-bX7CYaQ1Zw/edit?gid=0#gid=0\n  address_bar_focused: false\nwebpage:\n  header:\n    - type: container\n      id: header_main\n      bounds: {x: 0, y: 70, width: 1920, height: 118}\n      children:\n        - type: image\n          id: sheets_logo\n          label: Sheets home\n          bounds: {x: 12, y: 80, width: 40, height: 40}\n        - type: container\n          id: document_header\n          bounds: {x: 60, y: 80, width: 1860, height: 40}\n          children:\n            - type: text\n              id: doc_title\n              label: Brand List\n              bounds: {x: 70, y: 85, width: 100, height: 20}\n            - type: button\n              id: btn_star\n              label: null\n              bounds: {x: 175, y: 85, width: 24, height: 24}\n            - type: button\n              id: btn_move\n              label: null\n              bounds: {x: 205, y: 85, width: 24, height: 24}\n            - type: button\n              id: btn_cloud_status\n              label: null\n              bounds: {x: 235, y: 85, width: 24, height: 24}\n            - type: button\n              id: btn_history\n              label: null\n              bounds: {x: 1290, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_comments\n              label: null\n              bounds: {x: 1330, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_meet\n              label: null\n              bounds: {x: 1370, y: 85, width: 30, height: 30}\n            - type: button\n              id: btn_share\n              label: Share\n              bounds: {x: 1420, y: 85, width: 90, height: 36}\n            - type: image\n              id: user_avatar\n              label: null\n              bounds: {x: 1530, y: 85, width: 32, height: 32}\n        - type: navigation\n          id: main_menu\n          bounds: {x: 70, y: 115, width: 500, height: 30}\n          children:\n            - type: link\n              id: menu_file\n              label: File\n            - type: link\n              id: menu_edit\n              label: Edit\n            - type: link\n              id: menu_view\n              label: View\n            - type: link\n              id: menu_insert\n              label: Insert\n            - type: link\n              id: menu_format\n              label: Format\n            - type: link\n              id: menu_data\n              label: Data\n            - type: link\n              id: menu_tools\n              label: Tools\n            - type: link\n              id: menu_extensions\n              label: Extensions\n            - type: link\n              id: menu_help\n              label: Help\n        - type: container\n          id: toolbar\n          bounds: {x: 0, y: 148, width: 1920, height: 40}\n          children:\n            - type: button\n              id: btn_undo\n              label: null\n            - type: button\n              id: btn_redo\n              label: null\n            - type: button\n              id: btn_print\n              label: null\n            - type: button\n              id: btn_paint_format\n              label: null\n            - type: dropdown\n              id: dropdown_zoom\n              label: 100%\n            - type: dropdown\n              id: dropdown_format\n              label: '123'\n            - type: dropdown\n              id: dropdown_font\n              label: Default...\n            - type: dropdown\n              id: dropdown_font_size\n              label: '10'\n            - type: button\n              id: btn_bold\n              label: null\n            - type: button\n              id: btn_italic\n              label: null\n            - type: button\n              id: btn_strikethrough\n              label: null\n            - type: button\n              id: btn_text_color\n              label: null\n  main_content:\n    - type: container\n      id: spreadsheet_area\n      bounds: {x: 0, y: 188, width: 1850, height: 770}\n      children:\n        - type: container\n          id: formula_bar\n          bounds: {x: 0, y: 188, width: 1850, height: 30}\n          children:\n            - type: input\n              id: input_cell_selector\n              label: null\n              value: B9\n              bounds: {x: 10, y: 193, width: 50, height: 25}\n            - type: input\n              id: input_formula\n              label: null\n              value: Hisense\n              bounds: {x: 70, y: 193, width: 1780, height: 25}\n        - type: table\n          id: spreadsheet_grid\n          bounds: {x: 0, y: 223, width: 1850, height: 735}\n          headers: [A, B, C, D, E, F, G, H, I, J, K, L, M, N, O]\n          rows:\n            - id: row_1\n              cells:\n                - type: text\n                  id: cell_A1\n                  label: '#'\n                - type: text\n                  id: cell_B1\n                  label: Brand\n            - id: row_2\n              cells:\n                - type: text\n                  id: cell_A2\n                  label: '1'\n                - type: text\n                  id: cell_B2\n                  label: VStar\n            - id: row_3\n              cells:\n                - type: text\n                  id: cell_A3\n                  label: '2'\n                - type: text\n                  id: cell_B3\n                  label: BPL\n            - id: row_4\n              cells:\n                - type: text\n                  id: cell_A4\n                  label: '3'\n                - type: text\n                  id: cell_B4\n                  label: Godrej\n            - id: row_5\n              cells:\n                - type: text\n                  id: cell_A5\n                  label: '4'\n                - type: text\n                  id: cell_B5\n                  label: Intex\n            - id: row_6\n              cells:\n                - type: text\n                  id: cell_A6\n                  label: '5'\n                - type: text\n                  id: cell_B6\n                  label: Lloyd\n            - id: row_7\n              cells:\n                - type: text\n                  id: cell_A7\n                  label: '6'\n                - type: text\n                  id: cell_B7\n                  label: Lloyd\n            - id: row_8\n              cells:\n                - type: text\n                  id: cell_A8\n                  label: '7'\n                - type: text\n                  id: cell_B8\n                  label: IFB\n            - id: row_9\n              cells:\n                - type: text\n                  id: cell_A9\n                  label: '8'\n                - type: text\n                  id: cell_B9\n                  label: Hisense\n                  state: selected\n  sidebar:\n    - type: container\n      id: right_sidebar\n      bounds: {x: 1870, y: 223, width: 50, height: 735}\n      children:\n        - type: button\n          id: btn_calendar\n          label: null\n          bounds: {x: 1880, y: 260, width: 30, height: 30}\n        - type: button\n          id: btn_keep\n          label: null\n          bounds: {x: 1880, y: 310, width: 30, height: 30}\n        - type: button\n          id: btn_tasks\n          label: null\n          bounds: {x: 1880, y: 360, width: 30, height: 30}\n        - type: button\n          id: btn_contacts\n          label: null\n          bounds: {x: 1880, y: 410, width: 30, height: 30}\n        - type: button\n          id: btn_maps\n          label: null\n          bounds: {x: 1880, y: 460, width: 30, height: 30}\n        - type: button\n          id: btn_add_ons\n          label: null\n          bounds: {x: 1880, y: 530, width: 30, height: 30}\n  footer:\n    - type: container\n      id: sheet_navigation\n      bounds: {x: 0, y: 958, width: 1850, height: 30}\n      children:\n        - type: button\n          id: btn_add_sheet\n          label: null\n          bounds: {x: 10, y: 963, width: 24, height: 24}\n        - type: button\n          id: btn_all_sheets\n          label: null\n          bounds: {x: 40, y: 963, width: 24, height: 24}\n        - type: button\n          id: btn_sheet1\n          label: Sheet1\n          bounds: {x: 70, y: 960, width: 80, height: 28}\n          state: active\n```"]}]