browser_component:
  tab_title: "Guidewire InsuranceNow™"
  url: "ai.iscs.com/innovation"
  address_bar_focused: false
webpage:
  header:
    - component_type: header
      id: "main_header"
      bounds: {x: 0, y: 70, width: 1920, height: 60}
      subcomponents:
        - type: image
          label: "American Integrity logo"
          id: "logo_american_integrity"
          bounds: {x: 15, y: 80, width: 150, height: 40}
        - type: navigation
          id: "main_nav"
          bounds: {x: 1249, y: 89, width: 500, height: 30}
          children:
            - type: link
              label: "Home"
              id: "nav_home"
              bounds: {x: 1250, y: 90, width: 50, height: 20}
              state: active
            - type: link
              label: "Quote/Policy"
              id: "nav_quote_policy"
              bounds: {x: 1320, y: 90, width: 100, height: 20}
            - type: link
              label: "Claims"
              id: "nav_claims"
              bounds: {x: 1440, y: 90, width: 60, height: 20}
            - type: link
              label: "Cabinets"
              id: "nav_cabinets"
              bounds: {x: 1520, y: 90, width: 70, height: 20}
            - type: link
              label: "Support"
              id: "nav_support"
              bounds: {x: 1610, y: 90, width: 60, height: 20}
            - type: button
              label: "... MORE"
              id: "btn_more"
              bounds: {x: 1690, y: 90, width: 60, height: 20}
  sidebar:
    - component_type: sidebar
      id: "left_sidebar"
      bounds: {x: 0, y: 130, width: 250, height: 850}
      subcomponents:
        - type: input
          label: null
          id: "input_search"
          bounds: {x: 15, y: 145, width: 180, height: 35}
          value: "Search"
        - type: button
          label: null
          id: "btn_search"
          bounds: {x: 195, y: 145, width: 35, height: 35}
          children:
            - type: icon
              label: "Search"
              id: "icon_search"
        - type: text
          label: "ADVANCED SEARCH:"
          id: "text_advanced_search"
          bounds: {x: 15, y: 190, width: 120, height: 20}
        - type: link
          label: "POLICY"
          id: "link_policy"
          bounds: {x: 140, y: 190, width: 50, height: 20}
        - type: link
          label: "CLAIMS"
          id: "link_claims"
          bounds: {x: 200, y: 190, width: 50, height: 20}
        - type: navigation
          id: "sidebar_nav"
          bounds: {x: 0, y: 220, width: 250, height: 150}
          children:
            - type: link
              label: "News"
              id: "nav_news"
              bounds: {x: 15, y: 230, width: 220, height: 30}
              state: active
            - type: link
              label: "Inbox"
              id: "nav_inbox"
              bounds: {x: 15, y: 260, width: 220, height: 30}
              children:
                - type: badge
                  label: "152"
                  id: "badge_inbox"
                  bounds: {x: 200, y: 265, width: 30, height: 20}
            - type: link
              label: "Recent List"
              id: "nav_recent_list"
              bounds: {x: 15, y: 290, width: 220, height: 30}
    - component_type: sidebar
      id: "right_floating_sidebar"
      bounds: {x: 1880, y: 130, width: 40, height: 100}
      subcomponents:
        - type: button
          label: "WTRCRFT QUICK QT"
          id: "btn_quick_quote"
          bounds: {x: 1885, y: 140, width: 30, height: 40}
        - type: button
          label: "NEW QUOTE"
          id: "btn_new_quote"
          bounds: {x: 1885, y: 190, width: 30, height: 40}
  main_content:
    - component_type: main_content
      id: "main_content_area"
      bounds: {x: 260, y: 130, width: 1620, height: 850}
      subcomponents:
        - type: text
          label: "News & Announcements"
          id: "title_news_announcements"
          bounds: {x: 280, y: 150, width: 250, height: 30}
        - type: container
          id: "container_memorial_day"
          bounds: {x: 280, y: 200, width: 1600, height: 250}
          children:
            - type: text
              label: "Memorial Day Weekend Phone Coverage Updates"
              id: "title_memorial_day"
              bounds: {x: 280, y: 220, width: 600, height: 30}
            - type: text
              label: "In observance of the Memorial Day holiday, American Integrity Insurance will be closing at 2:00 pm eastern time on Friday, May 23 and will be closed on Monday, May 26. We will resume our regular business hours on Tuesday, May 27."
              id: "text_memorial_day_1"
              bounds: {x: 280, y: 270, width: 1600, height: 40}
            - type: text
              label: "Our answering service will accept messages for the remainder of the work day, and we will respond to messages as soon as possible upon our return to normal business hours on Tuesday May, 27. Our claims office, as always, will be available to your customers 24 hours a day at ************. Customers may also use our online Customer Portal to file a new claim or review the status of existing claims."
              id: "text_memorial_day_2"
              bounds: {x: 280, y: 320, width: 1600, height: 60}
              children:
                - type: link
                  label: "Customer Portal"
                  id: "link_customer_portal"
                  bounds: {x: 1000, y: 360, width: 120, height: 20}
            - type: text
              label: "Thank you, as always, for your flexibility and partnership."
              id: "text_memorial_day_3"
              bounds: {x: 280, y: 390, width: 1600, height: 20}
            - type: text
              label: "Need to contact us? Check out our Who To Call Guide to identify the best point of contact to assist with your needs."
              id: "text_memorial_day_4"
              bounds: {x: 280, y: 430, width: 1600, height: 20}
              children:
                - type: link
                  label: "Who To Call Guide"
                  id: "link_who_to_call"
                  bounds: {x: 550, y: 430, width: 120, height: 20}
        - type: container
          id: "container_webinar"
          bounds: {x: 280, y: 480, width: 1600, height: 400}
          children:
            - type: text
              label: "Navigating Challenges in the National Insurance Market Webinar"
              id: "title_webinar"
              bounds: {x: 280, y: 500, width: 800, height: 30}
            - type: text
              label: "Thursday, June 12 at 3:00 - 4:30pm EST"
              id: "subtitle_webinar"
              bounds: {x: 280, y: 540, width: 1600, height: 20}
            - type: text
              label: "Please join our CEO Bob Ritchie and special guest, Mark Friedlander of the Insurance Information Institute for an insightful discussion on the latest market trends, their impact, and solutions for navigating this complex landscape. Topics and Speakers Include:"
              id: "text_webinar_1"
              bounds: {x: 280, y: 570, width: 1600, height: 40}
            - type: list
              id: "list_webinar_speakers"
              bounds: {x: 300, y: 620, width: 1580, height: 100}
              children:
                - type: list_item
                  label: "National Weather Impacts - Bob Ritchie, CEO"
                  id: "item_speaker_1"
                - type: list_item
                  label: "National Legislative Landscape - Mark Friedlander, Triple-I Guest Speaker"
                  id: "item_speaker_2"
                - type: list_item
                  label: "American Integrity Market Response - Dick Dowd, EVP of Sales & Marketing"
                  id: "item_speaker_3"
                - type: list_item
                  label: "Florida Property Insurance Market Results - Brent Radeleff, EVP of Product, Pricing & Underwriting"
                  id: "item_speaker_4"
                - type: list_item
                  label: "Storm Trends - Natalie Ferrari, Emmy-winning meteorologist & Catastrophic Risk Analyst"
                  id: "item_speaker_5"
            - type: link
              label: "Click Here to Register for Our Webinar"
              id: "link_register_webinar"
              bounds: {x: 280, y: 730, width: 1600, height: 20}
            - type: text
              label: "Please note: If you previously registered, you will need to re-register."
              id: "text_webinar_2"
              bounds: {x: 280, y: 780, width: 1600, height: 20}
            - type: text
              label: "If you can't join, register anyway and we'll send you the slides following the webinar!"
              id: "text_webinar_3"
              bounds: {x: 280, y: 810, width: 1600, height: 20}
        - type: container
          id: "container_flood_capacity"
          bounds: {x: 280, y: 850, width: 1600, height: 100}
          children:
            - type: text
              label: "Flood Capacity Update"
              id: "title_flood_capacity"
              bounds: {x: 280, y: 870, width: 400, height: 30}
            - type: text
              label: "Our flood endorsement is currently available in all counties except Collier and Lee."
              id: "text_flood_capacity_1"
              bounds: {x: 280, y: 920, width: 1600, height: 20}