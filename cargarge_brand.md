# Car Garage Application - Vehicle Brand Management Instructions

## Objective
Log into the Car Garage application, navigate to the master data settings, and add a new vehicle brand to the system.

## Test Environment
- **Application URL**: http://3.110.125.122/
- **Test Credentials**: 
  - User ID: `<EMAIL>`
  - Password: (will be auto-populated)

## Step-by-Step Instructions

### Phase 1: Application Access and Login

**Step 1: Navigate to Application**
- Open your browser and go to: `http://3.110.125.122/`
- Wait for the Car Garage application login page to load completely

**Step 2: Enter Login Credentials**
- Click on the "User ID" text field in the login form
- Enter: `<EMAIL>`
- The password is : `ZZUBQYYV3K`
- Verify both fields are filled correctly

**Step 3: Complete Login Process**
- Click the "Login" button
- Wait for the login process to complete
- Verify successful login by checking for "Login Successful" notification
- Confirm navigation to the Job Card List page

### Phase 2: Navigate to Vehicle Brand Settings

**Step 4: Access Settings Menu**
- Locate the sidebar navigation on the left side of the screen
- Click on the "Settings" link
- Wait for navigation to the Master Department page

**Step 5: Switch to Vehicle Brand Management**
- Find the tab navigation within the settings area
- Click on the "Vehicle Brand" tab
- Verify the page switches to the Vehicle Brand management interface
- Confirm the URL changes to include `/master/vehiclebrand/1`

### Phase 3: Add New Vehicle Brand

**Step 6: Access Brand Creation Form**
- Locate the "Add Vehicle Brand" form section
- Click on the "Brand Name" input field to activate it
- Ensure the field is properly focused and ready for input

**Step 7: Enter Brand Information**


<!-- - Use web search and search `Top car brands in kuwait` and get the top 5 brand 
- if there is noweb search available stop the execution at this point Dont move forward -->
- open a new tab and go to this website `https://kuwait.hatla2ee.com/en/car/makes` and copy or get  the `{1}` brand mentioned in the website
- come back  to `http://3.110.125.122/master/vehiclebrand/1` 
- Click on the "Vehicle Brand" tab
- Locate the "Add Vehicle Brand" form section
- Click on the "Brand Name" input field to activate it
- click this "Brand Name" field and fill the  1st   car brand in the  "Brand Name" input field as fetched from the    `https://kuwait.hatla2ee.com/en/car/makes` website 
- Verify the text appears correctly in the field

then move to t
- Ensure no typing errors or formatting issues

**Step 8: Submit New Brand**
- Locate the "Add" button near the brand name field
- Click the "Add" button to submit the new vehicle brand
- Wait for a success notification to appear



## Expected Outcomes
- Successful login to the Car Garage application
- Smooth navigation through the settings menu structure
- Proper access to the Vehicle Brand management section
- Successful addition of "TVS motors" as a new vehicle brand
- Appropriate system notifications confirming the action

## Navigation Flow Summary
```
Login Page → Job Card List → Settings → Master Department → Vehicle Brand Management → Add New Brand
```

## Important Notes for Agent
- Ensure each page loads completely before proceeding to the next step
- Wait for notifications to appear after login and brand addition
- Verify URL changes match the expected navigation path
- The password field auto-populates, so focus on the User ID entry
- The Vehicle Brand tab is located within the master data settings area

