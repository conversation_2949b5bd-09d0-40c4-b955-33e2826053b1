browser_component:
  tab_title: "ai.iscs.com/innovation"
  url: "https://ai.iscs.com/innovation"
  address_bar_focused: false

webpage:
  navigation:
    - component_type: menu
      id: "main_nav"
      bounds: {x: 0, y: 0, width: 1200, height: 60}
      subcomponents:
        - component_type: link
          label: "Home"
          id: "nav_home"
          state: null
        - component_type: link
          label: "Quote/Policy"
          id: "nav_quote_policy"
          state: null
        - component_type: link
          label: "Claims"
          id: "nav_claims"
          state: null
        - component_type: link
          label: "Cabinets"
          id: "nav_cabinets"
          state: null
        - component_type: link
          label: "Support"
          id: "nav_support"
          state: null
  header:
    - component_type: text
      label: "News & Announcements"
      id: "header_news"
      bounds: {x: 100, y: 100, width: 200, height: 30}
  main_content:
    - component_type: text
      label: "Memorial Day Weekend Phone Coverage Updates"
      id: "main_memorial_day"
      bounds: {x: 100, y: 150, width: 500, height: 100}
    - component_type: text
      label: "Navigating Challenges in the National Insurance Market Webinar"
      id: "main_webinar"
      bounds: {x: 100, y: 300, width: 500, height: 100}
    - component_type: link
      label: "Click Here to Register for Our Webinar"
      id: "link_register"
      bounds: {x: 100, y: 450, width: 300, height: 30}
    - component_type: text
      label: "Flood Capacity Update"
      id: "main_flood"
      bounds: {x: 100, y: 500, width: 200, height: 30}
  sidebar:
    - component_type: text
      label: "News"
      id: "sidebar_news"
      bounds: {x: 10, y: 100, width: 50, height: 20}
    - component_type: text
      label: "Inbox"
      id: "sidebar_inbox"
      bounds: {x: 10, y: 130, width: 50, height: 20}
    - component_type: text
      label: "Recent List"
      id: "sidebar_recent"
      bounds: {x: 10, y: 160, width: 80, height: 20}