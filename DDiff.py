import yaml
from deepdiff import DeepDiff

# Function to load YAML file
def load_yaml(file_path):
    with open(file_path, 'r') as f:
        return yaml.safe_load(f)

# Load two YAMLs
yaml1 = load_yaml(r"E:\loveable_AI\bunch\ui_element_extraction_20250822_105018\ui_elements_0026.yaml")
yaml2 = load_yaml(r"E:\loveable_AI\bunch\ui_element_extraction_20250822_105018\ui_elements_0027.yaml")

# Compute delta
diff = DeepDiff(yaml1, yaml2, ignore_order=True,verbose_level=2)

# Print delta
print("Delta between YAMLs:")
print(diff)
