[{"image_name": "frame_0000.jpg", "success": "Able to generate UI elements", "tokens": 1974, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T20:55:04.450403", "attempt": 1}, {"image_name": "frame_0001.jpg", "error": "litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {\n  \"error\": {\n    \"code\": 503,\n    \"message\": \"The model is overloaded. Please try again later.\",\n    \"status\": \"UNAVAILABLE\"\n  }\n}\n", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-27T21:15:01.832187", "attempt": 1}, {"image_name": "frame_0001.jpg", "error": "litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {\n  \"error\": {\n    \"code\": 503,\n    \"message\": \"The model is overloaded. Please try again later.\",\n    \"status\": \"UNAVAILABLE\"\n  }\n}\n", "tokens": null, "wait_time": 60, "api_key_rotation": false, "timestamp": "2025-08-27T21:15:40.128518", "attempt": 2}, {"image_name": "frame_0001.jpg", "error": "litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {\n  \"error\": {\n    \"code\": 503,\n    \"message\": \"The model is overloaded. Please try again later.\",\n    \"status\": \"UNAVAILABLE\"\n  }\n}\n", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-27T21:47:25.888806", "attempt": 3}, {"image_name": "frame_0001.jpg", "error": "litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {\n  \"error\": {\n    \"code\": 503,\n    \"message\": \"The model is overloaded. Please try again later.\",\n    \"status\": \"UNAVAILABLE\"\n  }\n}\n", "tokens": null, "wait_time": 60, "api_key_rotation": false, "timestamp": "2025-08-27T21:48:01.067763", "attempt": 4}, {"image_name": "frame_0001.jpg", "error": "litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {\n  \"error\": {\n    \"code\": 503,\n    \"message\": \"The model is overloaded. Please try again later.\",\n    \"status\": \"UNAVAILABLE\"\n  }\n}\n", "tokens": null, "wait_time": 90, "api_key_rotation": false, "timestamp": "2025-08-27T21:49:13.137423", "attempt": 5}, {"image_name": "frame_0001.jpg", "success": "Able to generate UI elements", "tokens": 5276, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T21:53:34.999972", "attempt": 6}, {"image_name": "frame_0002.jpg", "success": "Able to generate UI elements", "tokens": 8577, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T21:53:56.144004", "attempt": 1}, {"image_name": "frame_0003.jpg", "success": "Able to generate UI elements", "tokens": 11878, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T21:54:29.556349", "attempt": 1}, {"image_name": "frame_0004.jpg", "success": "Able to generate UI elements", "tokens": 15339, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T21:55:07.587785", "attempt": 1}, {"image_name": "frame_0005.jpg", "success": "Able to generate UI elements", "tokens": 18800, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T21:55:41.458050", "attempt": 1}]