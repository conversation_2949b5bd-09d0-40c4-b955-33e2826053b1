
    server {
        listen 80 default_server;

        client_max_body_size 100M;

        location /product/ {
            proxy_pass http://product:3000/;
        }

        location /product/master/ {
            proxy_pass http://product-master:4000/;
        }

        location /service/ {
            proxy_pass http://service-master:5000/;
        }

        location /ticket/ {
            proxy_pass http://service-ticket:6000/;
        }

        location /authentication/ {
            proxy_pass http://service-authentication:7000/;
        }

        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri /index.html;
        }
    }

