[{"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='-WKtaLCuKrSLmtkP1Jrm6A8', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=3, prompt_tokens=1974, total_tokens=1977, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=3, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:02:09.269137", "attempt": 1}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {\n  \"error\": {\n    \"code\": 503,\n    \"message\": \"The model is overloaded. Please try again later.\",\n    \"status\": \"UNAVAILABLE\"\n  }\n}\n", "api_key_rotation": false, "timestamp": "2025-08-26T13:02:43.321547", "attempt": 2}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: VertexAIException InternalServerError - {\n  \"error\": {\n    \"code\": 500,\n    \"message\": \"An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting\",\n    \"status\": \"INTERNAL\"\n  }\n}\n", "api_key_rotation": false, "timestamp": "2025-08-26T13:03:47.642710", "attempt": 3}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='vmOtaIPwMK-BmtkPiZDhkQ4', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=62, prompt_tokens=1974, total_tokens=2036, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=62, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:05:26.105073", "attempt": 4}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: VertexAIException InternalServerError - {\n  \"error\": {\n    \"code\": 500,\n    \"message\": \"An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting\",\n    \"status\": \"INTERNAL\"\n  }\n}\n", "api_key_rotation": true, "timestamp": "2025-08-26T13:05:35.288799", "attempt": 5}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: VertexAIException InternalServerError - {\n  \"error\": {\n    \"code\": 500,\n    \"message\": \"An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting\",\n    \"status\": \"INTERNAL\"\n  }\n}\n", "api_key_rotation": false, "timestamp": "2025-08-26T13:06:08.578900", "attempt": 6}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='KWStaN2BOoOdz7IPl6rO6AI', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=15, prompt_tokens=1974, total_tokens=1989, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=15, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:07:13.738273", "attempt": 7}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='i2StaPypNrPfz7IP3emOmA4', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=34, prompt_tokens=1974, total_tokens=2008, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=34, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:08:51.613233", "attempt": 8}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='lWStaIH2MqrUz7IPoem2-QI', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=34, prompt_tokens=1974, total_tokens=2008, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=34, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": true, "timestamp": "2025-08-26T13:09:01.268378", "attempt": 9}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: VertexAIException InternalServerError - {\n  \"error\": {\n    \"code\": 500,\n    \"message\": \"An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting\",\n    \"status\": \"INTERNAL\"\n  }\n}\n", "api_key_rotation": false, "timestamp": "2025-08-26T13:09:35.099179", "attempt": 10}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: VertexAIException InternalServerError - {\n  \"error\": {\n    \"code\": 500,\n    \"message\": \"An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting\",\n    \"status\": \"INTERNAL\"\n  }\n}\n", "api_key_rotation": false, "timestamp": "2025-08-26T13:10:37.696603", "attempt": 11}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: VertexAIException InternalServerError - {\n  \"error\": {\n    \"code\": 500,\n    \"message\": \"An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting\",\n    \"status\": \"INTERNAL\"\n  }\n}\n", "api_key_rotation": false, "timestamp": "2025-08-26T13:12:10.495950", "attempt": 12}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='nGetaLmCErnVz7IPiN_T4Q0', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=41, prompt_tokens=1974, total_tokens=2015, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=41, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:21:55.811799", "attempt": 13}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='xWetaLXzIJCez7IPv4jB8AM', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=66, prompt_tokens=1974, total_tokens=2040, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=66, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:22:36.879339", "attempt": 14}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {\n  \"error\": {\n    \"code\": 503,\n    \"message\": \"The model is overloaded. Please try again later.\",\n    \"status\": \"UNAVAILABLE\"\n  }\n}\n", "api_key_rotation": false, "timestamp": "2025-08-26T13:23:41.964957", "attempt": 15}, {"image_name": "frame_0032.jpg", "error": "litellm.InternalServerError: VertexAIException InternalServerError - {\n  \"error\": {\n    \"code\": 500,\n    \"message\": \"An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting\",\n    \"status\": \"INTERNAL\"\n  }\n}\n", "api_key_rotation": false, "timestamp": "2025-08-26T13:25:15.146846", "attempt": 16}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='nGqtaP7FIZ2jqtsP6tCciA4', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=33, prompt_tokens=1974, total_tokens=2007, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=33, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:34:43.986114", "attempt": 17}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='wGqtaKCXH675qtsP19uuMQ', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=285, prompt_tokens=1974, total_tokens=2259, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=285, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:35:19.941411", "attempt": 18}, {"image_name": "frame_0032.jpg", "error": "Assistant response is None: ModelResponse(id='_2qtaIrfMqb-qtsPx8ju6Q8', created=**********, model='gemini-2.5-pro', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=None, role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=12, prompt_tokens=1974, total_tokens=1986, completion_tokens_details=CompletionTokensDetailsWrapper(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=12, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetailsWrapper(audio_tokens=None, cached_tokens=None, text_tokens=1716, image_tokens=None)), vertex_ai_grounding_metadata=[], vertex_ai_url_context_metadata=[], vertex_ai_safety_results=[], vertex_ai_citation_metadata=[])", "api_key_rotation": false, "timestamp": "2025-08-26T13:36:23.240298", "attempt": 19}]