import asyncio
import json
from dotenv import load_dotenv
from browser_use import Agent
from browser_use.llm import <PERSON><PERSON><PERSON><PERSON><PERSON>, ChatOpenAI  # Gemini via Google

load_dotenv()

# Load the intent (task) from a JSON file

async def main():
    # task = "{\"intent\":\"Theuser'soverallgoalistologintotheCarGarageapplication,navigatetothemasterdatasettings,andaddanewvehiclebrand.\",\"action_summary\":\"TheusersuccessfullyloggedintotheCarGarageapplication,navigatedthroughthesettingstotheVehicleBrandmanagementsection,andaddedanewvehiclebrandnamed\\\"Benz\\\".\",\"steps\":[{\"step_number\":1,\"action\":\"Userfocusedonthebrowser'saddressbar.\",\"details\":{\"target_element\":\"Addressandsearchbar(id:url_bar)\",\"cursor_position\":[654,88],\"page_url\":\"http://*************/\"}},{\"step_number\":2,\"action\":\"Usernavigatedtothe'CarGarage'application'sloginpage.\",\"details\":{\"target_element\":\"ApplicationLoad\",\"cursor_position\":[654,88],\"page_url\":\"http://*************\"}},{\"step_number\":3,\"action\":\"Userclickedthe'UserID'textfieldwithintheloginform.\",\"details\":{\"target_element\":\"UserID(id:input_user_id)\",\"cursor_position\":[414,468],\"page_url\":\"http://*************\"}},{\"step_number\":4,\"action\":\"Userentered'<EMAIL>'intothe'UserID'fieldandthenenteredapasswordintothe'Password'field.\",\"details\":{\"target_element\":\"Password(id:input_password)\",\"cursor_position\":[846,546],\"page_url\":\"http://*************\"}},{\"step_number\":5,\"action\":\"Userclickedthe'Login'button,successfullylogginginandnavigatingtotheJobCardListpage.A'LoginSuccessful'notificationappeared.\",\"details\":{\"target_element\":\"Login(id:btn_login)\",\"cursor_position\":[508,720],\"page_url\":\"http://*************/jobcard-list/1\"}},{\"step_number\":6,\"action\":\"Userclickedthe'Settings'linkinthesidebarnavigation,whichnavigatedtotheMasterDepartmentpage.\",\"details\":{\"target_element\":\"Settings(id:link_settings)\",\"cursor_position\":[443,647],\"page_url\":\"http://*************/master/department/1\"}},{\"step_number\":7,\"action\":\"Userclickedthe'VehicleBrand'tab,switchingtheviewtotheVehicleBrandmanagementpage.\",\"details\":{\"target_element\":\"VehicleBrand(id:tab_vehicle_brand)\",\"cursor_position\":[852,474],\"page_url\":\"http://*************/master/vehiclebrand/1\"}},{\"step_number\":8,\"action\":\"Userclickedthe'BrandName'inputfieldinthe'AddVehicleBrand'form.\",\"details\":{\"target_element\":\"BrandName(id:input_brand_name)\",\"cursor_position\":[852,474],\"page_url\":\"http://*************/master/vehiclebrand/1\"}},{\"step_number\":9,\"action\":\"Usertyped'Benz'intothe'BrandName'inputfield.\",\"details\":{\"target_element\":\"BrandName(id:input_brand_name)\",\"cursor_position\":[1292,529],\"page_url\":\"http://*************/master/vehiclebrand/1\"}},{\"step_number\":10,\"action\":\"notificationappeared.\",\"details\":{\"target_element\":\"Add(id:btn_add)\",\"cursor_position\":[1282,528],\"page_url\":\"http://*************/master/vehiclebrand/1\"}}]}, for password use - ZZUBQYYV3K"  # Read from local file
    task = """
# Car Garage Application - Vehicle Brand Management Instructions

## Objective
Log into the Car Garage application, navigate to the master data settings, and add a new vehicle brand to the system.

## Test Environment
- **Application URL**: http://*************/
- **Test Credentials**: 
  - User ID: `<EMAIL>`
  - Password: `ZZUBQYYV3K`

## Step-by-Step Instructions

### Phase 1: Application Access and Login

**Step 1: Navigate to Application**
- Open your browser and go to: `http://*************/`
- Wait for the Car Garage application login page to load completely

**Step 2: Enter Login Credentials**
- Click on the "User ID" text field in the login form
- Enter: `<EMAIL>`
- The password field should automatically populate
- Verify both fields are filled correctly

**Step 3: Complete Login Process**
- Click the "Login" button
- Wait for the login process to complete
- Verify successful login by checking for "Login Successful" notification
- Confirm navigation to the Job Card List page

### Phase 2: Navigate to Vehicle Brand Settings

**Step 4: Access Settings Menu**
- Locate the sidebar navigation on the left side of the screen
- Click on the "Settings" link
- Wait for navigation to the Master Department page

**Step 5: Switch to Vehicle Brand Management**
- Find the tab navigation within the settings area
- Click on the "Vehicle Brand" tab
- Verify the page switches to the Vehicle Brand management interface
- Confirm the URL changes to include `/master/vehiclebrand/1`

### Phase 3: Add New Vehicle Brand

**Step 6: Access Brand Creation Form**
- Locate the "Add Vehicle Brand" form section
- Click on the "Brand Name" input field to activate it
- Ensure the field is properly focused and ready for input

**Step 7: Enter Brand Information**
- Type `Tata motors 1` into the "Brand Name" input field
- Verify the text appears correctly in the field
- Ensure no typing errors or formatting issues

**Step 8: Submit New Brand**
- Locate the "Add" button near the brand name field
- Click the "Add" button to submit the new vehicle brand
- Wait for a success notification to appear

## Expected Outcomes
- Successful login to the Car Garage application
- Smooth navigation through the settings menu structure
- Proper access to the Vehicle Brand management section
- Successful addition of "Tata motors 1" as a new vehicle brand
- Appropriate system notifications confirming the action

## Navigation Flow Summary
```
Login Page → Job Card List → Settings → Master Department → Vehicle Brand Management → Add New Brand
```

## Important Notes for Agent
- Ensure each page loads completely before proceeding to the next step
- Wait for notifications to appear after login and brand addition
- Verify URL changes match the expected navigation path
- The password field auto-populates, so focus on the User ID entry
- The Vehicle Brand tab is located within the master data settings area


"""
    agent = Agent(
        task=task,
        llm=ChatGoogle(model="gemini-2.5-pro", temperature=0),
        # llm=ChatOpenAI(model="gpt-4o-mini", temperature=0.2),
    )
    await agent.run()

asyncio.run(main())