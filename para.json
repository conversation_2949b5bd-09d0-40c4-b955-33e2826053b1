{
    "intent": "The user's overall goal is to log into the {{application_name}} application, navigate to the master data settings, and add a new vehicle brand.",
    "action_summary": "The user successfully logged into the {{application_name}} application, navigated through the settings to the Vehicle Brand management section, and added a new vehicle brand named \"{{vehicle_brand_name}}\".",
    "steps": [
        {
            "step_number": 1,
            "action": "User focused on the browser's address bar.",
            "details": {
                "target_element": "Address and search bar (id: {{address_bar_id}})",
                "cursor_position": [
                    {{cursor_x_1}},
                    {{cursor_y_1}}
                ],
                "page_url": "{{base_url}}/"
            }
        },
        {
            "step_number": 2,
            "action": "User navigated to the '{{application_name}}' application's login page.",
            "details": {
                "target_element": "Application Load",
                "cursor_position": [
                    {{cursor_x_2}},
                    {{cursor_y_2}}
                ],
                "page_url": "{{base_url}}"
            }
        },
        {
            "step_number": 3,
            "action": "User clicked the 'User ID' text field within the login form.",
            "details": {
                "target_element": "User ID (id: {{user_id_field_id}})",
                "cursor_position": [
                    {{cursor_x_3}},
                    {{cursor_y_3}}
                ],
                "page_url": "{{base_url}}"
            }
        },
        {
            "step_number": 4,
            "action": "User entered '{{user_email}}' into the 'User ID' field and then entered a password into the 'Password' field.",
            "details": {
                "target_element": "Password (id: {{password_field_id}})",
                "cursor_position": [
                    {{cursor_x_4}},
                    {{cursor_y_4}}
                ],
                "page_url": "{{base_url}}"
            }
        },
        {
            "step_number": 5,
            "action": "User clicked the 'Login' button, successfully logging in and navigating to the Job Card List page. A '{{login_success_message}}' notification appeared.",
            "details": {
                "target_element": `Login (id: {{login_button_id}})`,
                "cursor_position": [
                    {{cursor_x_5}},
                    {{cursor_y_5}}
                ],
                "page_url": "{{base_url}}/jobcard-list/{{jobcard_list_id}}"
            }
        },
        {
            "step_number": 6,
            "action": "User clicked the 'Settings' link in the sidebar navigation, which navigated to the Master Department page.",
            "details": {
                "target_element": "Settings (id: {{settings_link_id}})",
                "cursor_position": [
                    {{cursor_x_6}},
                    {{cursor_y_6}}
                ],
                "page_url": "{{base_url}}/master/department/{{department_id}}"
            }
        },
        {
            "step_number": 7,
            "action": "User clicked the 'Vehicle Brand' tab, switching the view to the Vehicle Brand management page.",
            "details": {
                "target_element": "Vehicle Brand (id: {{vehicle_brand_tab_id}})",
                "cursor_position": [
                    {{cursor_x_7}},
                    {{cursor_y_7}}
                ],
                "page_url": "{{base_url}}/master/vehiclebrand/{{vehiclebrand_id}}"
            }
        },
        {
            "step_number": 8,
            "action": "User clicked the 'Brand Name' input field in the 'Add Vehicle Brand' form.",
            "details": {
                "target_element": "Brand Name (id: {{brand_name_input_id}})",
                "cursor_position": [
                    {{cursor_x_8}},
                    {{cursor_y_8}}
                ],
                "page_url": "{{base_url}}/master/vehiclebrand/{{vehiclebrand_id}}"
            }
        },
        {
            "step_number": 9,
            "action": "User typed '{{vehicle_brand_name}}' into the 'Brand Name' input field.",
            "details": {
                "target_element": "Brand Name (id: {{brand_name_input_id}})",
                "cursor_position": [
                    {{cursor_x_9}},
                    {{cursor_y_9}}
                ],
                "page_url": "{{base_url}}/master/vehiclebrand/{{vehiclebrand_id}}"
            }
        },
        {
            "step_number": 10,
            "action": "A '{{brand_add_notification}}' notification appeared.",
            "details": {
                "target_element": "Add (id: {{add_button_id}})",
                "cursor_position": [
                    {{cursor_x_10}},
                    {{cursor_y_10}}
                ],
                "page_url": "{{base_url}}/master/vehiclebrand/{{vehiclebrand_id}}"
            }
        }
    ]
}
