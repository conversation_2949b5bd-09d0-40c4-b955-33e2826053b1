browser_component:
  tab_title: "ai.iscs.com/innovation"
  url: "https://ai.iscs.com/innovation"
  address_bar_focused: false

webpage:
  navigation:
    - component_type: menu
      id: "main_nav"
      bounds: {x: 0, y: 0, width: 1200, height: 60}
      subcomponents:
        - component_type: link
          label: "Home"
          id: "nav_home"
          state: null
        - component_type: link
          label: "Quote/Policy"
          id: "nav_quote_policy"
          state: null
        - component_type: link
          label: "Claims"
          id: "nav_claims"
          state: null
        - component_type: link
          label: "Cabinets"
          id: "nav_cabinets"
          state: null
        - component_type: link
          label: "Support"
          id: "nav_support"
          state: null
  header:
    - component_type: text
      label: "News & Announcements"
      id: "header_news"
      bounds: {x: 100, y: 100, width: 200, height: 30}
  main_content:
    - component_type: text
      label: "Memorial Day Weekend Phone Coverage Updates"
      id: "main_memorial_day"
      bounds: {x: 100, y: 150, width: 500, height: 100}
    - component_type: text
      label: "In observance of the Memorial Day holiday, American Integrity Insurance will be closing at 2:00 pm eastern time on Friday, May 23 and will be closed on Monday, May 26. We will resume our regular business hours on Tuesday, May 27."
      id: "memorial_day_details"
      bounds: {x: 100, y: 180, width: 500, height: 80}
    - component_type: text
      label: "Our answering service will accept messages for the remainder of the work day, and we will respond to messages as soon as possible upon our return to normal business hours on Tuesday, May 27."
      id: "answering_service"
      bounds: {x: 100, y: 260, width: 500, height: 80}
    - component_type: text
      label: "Our claims office, as always, will be available to your customers 24 hours a day at ************. Customers may also use our online Customer Portal to file a new claim or review the status of existing claims."
      id: "claims_office"
      bounds: {x: 100, y: 340, width: 500, height: 80}
    - component_type: text
      label: "Thank you, as always, for your flexibility and partnership."
      id: "thank_you"
      bounds: {x: 100, y: 420, width: 500, height: 30}
    - component_type: text
      label: "Need to contact us? Check out our Who To Call Guide to identify the best point of contact to assist with your needs."
      id: "contact_us"
      bounds: {x: 100, y: 460, width: 500, height: 30}
    - component_type: text
      label: "Navigating Challenges in the National Insurance Market Webinar"
      id: "webinar_title"
      bounds: {x: 100, y: 520, width: 500, height: 30}
    - component_type: text
      label: "Thursday, June 12 at 3:00 - 4:30pm EST"
      id: "webinar_time"
      bounds: {x: 100, y: 550, width: 500, height: 30}
    - component_type: text
      label: "Please join our CEO Bob Ritchie and special guest, Mark Friedlander of the Insurance Information Institute for an insightful discussion on the latest market trends, their impact, and solutions for navigating this complex landscape."
      id: "webinar_description"
      bounds: {x: 100, y: 580, width: 500, height: 80}
    - component_type: link
      label: "Click Here to Register for Our Webinar"
      id: "register_link"
      bounds: {x: 100, y: 680, width: 300, height: 30}
    - component_type: text
      label: "Flood Capacity Update"
      id: "flood_update"
      bounds: {x: 100, y: 730, width: 200, height: 30}
    - component_type: text
      label: "Our flood endorsement is currently available in all counties except Collier and Lee."
      id: "flood_details"
      bounds: {x: 100, y: 760, width: 500, height: 30}
  sidebar:
    - component_type: text
      label: "News"
      id: "sidebar_news"
      bounds: {x: 10, y: 100, width: 50, height: 20}
    - component_type: text
      label: "Inbox"
      id: "sidebar_inbox"
      bounds: {x: 10, y: 130, width: 50, height: 20}
    - component_type: text
      label: "Recent List"
      id: "sidebar_recent"
      bounds: {x: 10, y: 160, width: 80, height: 20}