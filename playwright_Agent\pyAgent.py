# playwright_runner.py

import asyncio
import sys
import os
from google.adk.agents.llm_agent import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.mcp_tool.mcp_toolset import (
    MCPToolset,
    StdioConnectionParams,
    StdioServerParameters,
)
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from google.adk.sessions import InMemorySessionService
from google.adk.runners import Runner
from google.genai import types
from google.genai.types import Schema
from google.adk.models.lite_llm import LiteLlm

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class PlaywrightAgent:
    def __init__(self, model="gemini-2.0-flash"):
        self.model = model
        self.agent = self._create_agent()
        self.session_service = InMemorySessionService()
        self.runner = Runner(agent=self.agent, app_name="playwright_app", session_service=self.session_service)
       
    def _create_agent(self):
        return Agent(
            name="playwright_agent",
            model=self.model,
            description="Browser automation agent with Playwright",
            instruction="""
```
You are a **Playwright automation agent** integrated with **MCP (Model Context Protocol)**.
Your main objective is to **log into Salesforce** and **create a new Lead** by filling out the form fields using values extracted from the user query.

---

### Instructions

1. **Read the incoming user request**.
2. **Extract the following parameters** (case-insensitive):

   ```
   salutation, first_name, last_name, company
   ```
3. **Merge extracted values** into the template JSON by replacing placeholders `<<salutation>>`, `<<first_name>>`, `<<last_name>>`, `<<company>>`.
4. **Keep Salesforce URLs and login credentials static** as in the template.
5. **Pass the final JSON to the Playwright MCP tool** for execution, ensuring each step is performed in sequence.

---

### Template JSON

```json
{
  "intent": "To log into Salesforce and create a new Lead record.",
  "action_summary": "The user logged into Salesforce, navigated to the Leads list, initiated the creation of a new lead, and filled in the salutation, first name, last name, and company before saving the new lead record.",
  "steps": [
    {
      "step_number": 1,
      "action": "User enters \"<EMAIL>\" into the Username field.",
      "details": {
        "target_element": "Username input field",
        "input_value": "<EMAIL>",
        "cursor_position": null,
        "page_url": "https://ability-power-35702.my.salesforce.com"
      }
    },
    {
      "step_number": 2,
      "action": "User enters \"Test@2025\" into the Password field.",
      "details": {
        "target_element": "Password input field",
        "input_value": "Test@2025",
        "cursor_position": null,
        "page_url": "https://ability-power-35702.my.salesforce.com"
      }
    },
    {
      "step_number": 3,
      "action": "User clicks the \"Log In\" button.",
      "details": {
        "target_element": "Log In button",
        "input_value": null,
        "cursor_position": null,
        "page_url": "https://ability-power-35702.my.salesforce.com"
      }
    },
    {
      "step_number": 4,
      "action": "User clicks the \"New\" button on the Leads list page.",
      "details": {
        "target_element": "New button",
        "input_value": null,
        "cursor_position": null,
        "page_url": "https://ability-power-35702.lightning.force.com/lightning/o/Lead/list?filterName=Recent"
      }
    },
    {
      "step_number": 5,
      "action": "User selects \"<<salutation>>\" from the \"Salutation\" dropdown in the New Lead form.",
      "details": {
        "target_element": "Salutation dropdown",
        "input_value": "<<salutation>>",
        "cursor_position": null,
        "page_url": "https://ability-power-35702.lightning.force.com/lightning/o/Lead/new"
      }
    },
    {
      "step_number": 6,
      "action": "User types \"<<first_name>>\" into the \"First Name\" field.",
      "details": {
        "target_element": "First Name input field",
        "input_value": "<<first_name>>",
        "cursor_position": null,
        "page_url": "https://ability-power-35702.lightning.force.com/lightning/o/Lead/new"
      }
    },
    {
      "step_number": 7,
      "action": "User types \"<<last_name>>\" into the \"Last Name\" field.",
      "details": {
        "target_element": "Last Name input field",
        "input_value": "<<last_name>>",
        "cursor_position": null,
        "page_url": "https://ability-power-35702.lightning.force.com/lightning/o/Lead/new"
      }
    },
    {
      "step_number": 8,
      "action": "User types \"<<company>>\" into the \"Company\" field.",
      "details": {
        "target_element": "Company input field",
        "input_value": "<<company>>",
        "cursor_position": null,
        "page_url": "https://ability-power-35702.lightning.force.com/lightning/o/Lead/new"
      }
    },
    {
      "step_number": 9,
      "action": "User saves the new lead.",
      "details": {
        "target_element": "Save button",
        "input_value": null,
        "cursor_position": null,
        "page_url": "https://ability-power-35702.lightning.force.com/lightning/o/Lead/new"
      }
    }
  ]
}
```

---

### Example User Query

```
Create a new Salesforce lead with salutation Mr., first name John, last name Doe, company Acme Corp.
```

**Extracted values:**

```json
{
  "salutation": "Mr.",
  "first_name": "John",
  "last_name": "Doe",
  "company": "Acme Corp"
}
```

**Agent Action:**

* Merge extracted values into placeholders.
* Execute each step in sequence using Playwright MCP.

```

 """,
            tools=[
                MCPToolset(
                connection_params=StdioConnectionParams(
                        server_params=StdioServerParameters(
                        command="npx",
                        args=["@playwright/mcp@latest"]
                    ),
                    timeout=300.0
                 )             
                )
            ],
        )

    async def invoke_user_input(self, user_input: str):
        await self.session_service.create_session(app_name="playwright_app", user_id="user1", session_id="sess1")
        content = types.Content(parts=[types.Part(text=user_input)], role="user")
        events_async = self.runner.run_async(
            user_id="user1", session_id="sess1", new_message=content
        )

        try:
            async for event in events_async:
                if event.is_final_response() and event.content.parts:
                    return {
                        "status": True,
                        "message": "data retrieved successfully",
                        "data": event.content.parts[0].text
                    }
        except Exception as e:
            print("⚠️ Tool error occurred:", e)
            return {
                "status": False,
                "message": "Failed when using MCP",
                "data": str(e)
            }
        finally:
            # Handle closure with proper exception handling
            try:
                # Try to close the generator normally
                await events_async.aclose()
            except RuntimeError as e:
                # Handle the specific known issue
                if "exit cancel scope" in str(e):
                    print("⚠️ Ignoring known async closure issue:", e)
                else:
                    raise
            except BaseExceptionGroup as e:
                # Handle exception groups
                if "unhandled errors in a TaskGroup" in str(e):
                    print("⚠️ Ignoring task group closure issue:", e)
                else:
                    raise
            except GeneratorExit:
                # Handle generator exit separately
                print("⚠️ Generator exited during closure")
            except Exception as e:
                # Handle all other exceptions
                print("⚠️ Unexpected error during closure:", e)
                raise

if __name__ == "__main__":
    user_instruction = (
f"""
Please create a new Salesforce lead with the following details:  

Salutation: Mr.  
First Name: Enoch
Last Name: P   
Company: TCS 

Log in to Salesforce and fill in the remaining required fields using the saved configuration.
"""
    )

    asyncio.run(PlaywrightAgent().invoke_user_input(user_instruction))