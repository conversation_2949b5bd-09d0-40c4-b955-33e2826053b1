import os
import yaml
import time
import re
from pathlib import Path
import google.generativeai as genai
from typing import List
import PIL.Image
import json
import argparse
import datetime
import winsound #to be removed for developing
from google.generativeai.types import HarmCategory, HarmBlockThreshold

class SimpleScreenshotAnalyzer:
    def __init__(self, api_key: str = None):
        """Initialize with Google GenAI."""
        genai.configure(api_key=api_key or os.getenv('GOOGLE_API_KEY'))

        # Configure safety settings to be more permissive for screenshot analysis
        self.safety_settings  = {
    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
}



        self.user_prompt = """
        You are an expert UI analyst. Your task is to deconstruct a given screenshot (frame) of a web application and generate a structured representation of its components in valid YAML format.


        ### **1. Core Rules**

        1.  **Classify the frame into two primary regions:**
            * `browser_component`: This region contains the browser's chrome, specifically the active tab title and the URL in the address bar.
            * `webpage`: This is the main viewport containing the rendered website content.

        2.  **Extract Browser Information:**
            * From `browser_component`, extract the `tab_title` and the `url`.

        3.  **Deconstruct the Webpage:**
            * Identify and classify all visible subcomponents within the `webpage` region into one of the following types:
                * `menu/navigation_bar`: Top or side navigation menus.
                * `header/banner`: Logos, branding, or site-wide messages.
                * `workspace`: The main interactive area containing forms, tables, media, etc.
                * `sidebar/drawer`: Collapsible panels, filters, or secondary menus.
                * `footer`: Bottom section with copyright, links, contact info.
                * `modal/popup`: Overlays, dialogs, or confirmation boxes.
                * `notifications/toasts`: Temporary system messages.

            * For each subcomponent, extract all visible **UI elements** (buttons, inputs, links, etc.). Represent each element in YAML with the following attributes:
                * `type`: The type of element (e.g., `button`, `text_field`, `dropdown`, `table`, `icon`).
                * `label`: The visible text or accessible name of the element.
                * `id`: A unique, generated identifier (e.g., "btn_submit", "input_email_1").
                * `position`: The `{x, y}` coordinates of the top-left corner relative to the webpage viewport.
                * `size`: The `{width, height}` of the element in pixels.
                * `elements_within` (optional): For container elements (like a navigation bar or actions cell), list the nested elements.
                * `state` (optional): The current state if visually distinct (e.g., `hovered`, `active`, `disabled`, `selected`).

        ---

        ### **2. Special Handling for Tables**

        If a `table` is detected within the `workspace`, it must be structured as follows:
        * `type: table`
        * `headers`: A list of the column header strings.
        * `rows`: A list of row objects.
            * Each row must have an `id` (e.g., "row_1").
            * Each row contains a list of `cells`.
            * Each cell can be a simple text element `{type: text, label: "...", id: "..."}`.
            * If a cell contains action icons or buttons (e.g., edit, delete, view), its `type` must be `actions` and it must contain an `elements_within` list detailing each clickable element (icon, button, etc.).

        ---

        ### **3.  Error Handling**

        **This is a critical rule.** If the provided image **cannot be analyzed**—because it is blurry, corrupted, not a web application screenshot, or otherwise unparsable—you MUST return the following specific YAML structure. Do not attempt to guess the content.

        ```yaml
        error:
        type: "ANALYSIS_FAILED"
        message: "The provided image could not be analyzed. It may be blurry, corrupted, or not a valid web application screenshot."
        ```

        -----

        ### **4. Examples**

        #### ** Success Case Example (with a table)**

        ```yaml
        browser_component:
        tab_title: "Users - Admin Panel"
        url: "[https://example.com/admin/users](https://example.com/admin/users)"
        webpage:
        workspace:
            - type: table
            label: "User List"
            id: "table_users_1"
            position: { x: 100, y: 200 }
            size: { width: 800, height: 400 }
            headers: ["ID", "Name", "Email", "Actions"]
            rows:
                - id: "row_1"
                cells:
                    - { type: text, label: "1", id: "cell_1_1" }
                    - { type: text, label: "Alice", id: "cell_1_2" }
                    - { type: text, label: "<EMAIL>", id: "cell_1_3" }
                    - type: actions
                    id: "cell_1_4"
                    elements_within:
                        - type: icon
                        label: "view"
                        id: "icon_view_1"
                        position: { x: 750, y: 230 }
                        size: { width: 20, height: 20 }
                        - type: icon
                        label: "edit"
                        id: "icon_edit_1"
                        position: { x: 780, y: 230 }
                        size: { width: 20, height: 20 }
        ```

        #### ** Error Case Example**

        ```yaml
        error:
        type: "ANALYSIS_FAILED"
        message: "The provided image could not be analyzed. It may be blurry, corrupted, or not a valid web application screenshot."
        ```

        -----

        ### **5. Final Instruction**

        Analyze the provided frame and generate **ONLY the valid YAML output** based on the rules and structure defined above. Do not include any explanations, apologies, or conversational text in your response.

        """

        self.system_prompt = """You are an expert in analyzing browser screenshots. 

        Always follow the user instructions exactly. 

        Rules:
        - Treat each input as a browser screenshot (frame).  
        - Extract information into two sections only: `browser_component` and `webpage`.  
        - Output must be valid YAML only — no explanations, no extra text.  
        - If data is missing or not visible, set the value to `null`.  
        - Capture all visible details in the screenshot UI.  
        - Represent the UI in a hierarchical tree structure using `component_type` and `subcomponents`. """


# 

        # Configure generation settings for better reliability
        self.generation_config = {
            "temperature": 0.1,
            "top_p": 0.95,
            "top_k": 40,
            "max_output_tokens": 8192,
        }

        self.model = genai.GenerativeModel(
            'gemini-2.5-pro',
            safety_settings=self.safety_settings,
            generation_config=self.generation_config
        )

        self.chat = None  # For context tracking

        # Simple system prompt
        self.system_prompt1 = """You are an expert in analyzing browser screenshots. 

Always follow the user instructions exactly. 

Rules:
- Treat each input as a browser screenshot (frame).  
- Extract information into two sections only: `browser_component` and `webpage`.  
- Output must be valid YAML only — no explanations, no extra text.  
- If data is missing or not visible, set the value to `null`.  
- Capture all visible details in the screenshot UI.  
- Represent the UI in a hierarchical tree structure using `component_type` and `subcomponents`. """

    def extract_frame_id_from_filename(self, filename: str) -> tuple:
        """
        Extract frame ID and timestamp from filename patterns like:
        - frame_0000_001.jpg -> returns ("0000", "001")
        - frame_0001_012.jpg -> returns ("0001", "012")
        - frame_0000.jpg -> returns ("0000", None)
        """
        # Pattern to match frame_XXXX_YYY (with timestamp)
        pattern_with_timestamp = r'frame_(\d{4})_(\d{3})'
        match = re.search(pattern_with_timestamp, filename)
        if match:
            return match.group(1), match.group(2)  # Return frame ID and timestamp

        # Pattern to match frame_XXXX (without timestamp)
        pattern_without_timestamp = r'frame_(\d{4})'
        match = re.search(pattern_without_timestamp, filename)
        if match:
            return match.group(1), None  # Return frame ID only

        return "0000", None  # Default frame ID

    def generate_output_filename(self, input_path: str, suffix: str = "analysis") -> str:
        """
        Generate output filename with frame ID and timestamp.
        Examples:
        - frame_0000_001.jpg -> ui_elements_0000_001.yaml
        - frame_0001_012.jpg -> ui_elements_0001_012.yaml
        - frame_0000.jpg -> ui_elements_0000.yaml
        """
        input_name = Path(input_path).stem  # Get filename without extension
        frame_id, timestamp = self.extract_frame_id_from_filename(input_name)

        # Include timestamp in filename if available
        if timestamp:
            return f"ui_elements_{frame_id}_{timestamp}.yaml"
        else:
            return f"ui_elements_{frame_id}.yaml"

    def handle_response_error(self, response, image_path: str) -> str:
        """Handle various response errors and return appropriate YAML."""
        try:
            # Check if response has candidates
            if not response.candidates:
                return f"# Error: No candidates returned for {Path(image_path).name}\nerror: 'No response candidates generated'"

            candidate = response.candidates[0]

            # Check finish reason
            finish_reason = candidate.finish_reason

            if finish_reason == 1:  # STOP
                # This should be normal, but text might be empty
                if hasattr(candidate.content, 'parts') and candidate.content.parts:
                    return candidate.content.parts[0].text
                else:
                    return f"# Error: Empty response for {Path(image_path).name}\nerror: 'Response generated but no text content'"
            elif finish_reason == 2:  # MAX_TOKENS
                return f"# Error: Max tokens reached for {Path(image_path).name}\nerror: 'Response truncated due to token limit'"
            elif finish_reason == 3:  # SAFETY
                return f"# Error: Safety filter triggered for {Path(image_path).name}\nerror: 'Content blocked by safety filters'"
            elif finish_reason == 4:  # RECITATION
                return f"# Error: Recitation filter triggered for {Path(image_path).name}\nerror: 'Content blocked due to recitation concerns'"
            else:
                return f"# Error: Unknown finish reason {finish_reason} for {Path(image_path).name}\nerror: 'Unexpected response termination'"

        except Exception as e:
            return f"# Error processing response for {Path(image_path).name}\nerror: {str(e)}"


    def _is_valid_yaml(self, content: str) -> bool:
        """Lightweight check to see if content looks like valid YAML (and not an error)."""
        try:
            if not content or content.strip().startswith("# Error"):
                return False
            # Try parsing to ensure it's syntactically valid YAML
            yaml.safe_load(content)
            return True
        except Exception:
            return False

    def analyze_screenshot(self, image_path: str, max_retries: int = 3) -> str:
        """Analyze single screenshot with retry logic."""
        for attempt in range(max_retries):
            try:
                image = PIL.Image.open(image_path)
                response = self.model.generate_content([self.system_prompt, image])
                print("response.text >>>>: <<<<<:",response)
                # Handle response with error checking
                try:
                    result = response.text.strip()
                except Exception:
                    # Handle cases where response.text fails
                    result = self.handle_response_error(response, image_path)

                # Clean markdown and stray characters like backticks
                if result.startswith("```yaml"):
                    result = result.replace("```yaml", "").replace("```", "").strip()
                elif result.startswith("```"):
                    result = result.replace("```", "").strip()

                # Strip any leading backticks or junk
                result = result.lstrip("` \n\r\t")

                # Check if YAML is valid before returning
                if not self._is_valid_yaml(result):
                    print(f"Invalid YAML detected for {Path(image_path).name} (first 200 chars): {repr(result[:200])}")
                    raise ValueError("Model returned invalid YAML content")

                return result



            except Exception as e:
                print(f"Attempt {attempt + 1}/{max_retries} failed for {image_path}: {e}")
                if attempt < max_retries - 1:
                    # Exponential backoff: 2^attempt seconds
                    wait_time = 2 ** attempt
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                else:
                    return f"# Error analyzing {Path(image_path).name} after {max_retries} attempts\nerror: {str(e)}"

    def process_images_with_context(self, image_paths: List[str], output_folder: str = "yaml_outputs", isIntent: str = None):
        """Process images with context tracking and metadata management."""
        # Import YamlDiffProcessor here to avoid circular imports
        from yamlDiff import YamlDiffProcessor

        Path(output_folder).mkdir(exist_ok=True)

        # # Load existing metadata
        # metadata = self.load_metadata()

        # Start chat for context with safety settings
        self.chat = self.model.start_chat()

        try:
            initial_response = self.chat.send_message(f"{self.system_prompt}\n\nI'll send you {len(image_paths)} screenshots. Remember context between them.")
            print("✓ Chat context initialized")
        except Exception as e:
            print(f"⚠️ Warning: Could not initialize chat context: {e}")
            return {"status": False, "error_stage": "gemini_chat_init", "error_message": str(e)}

        # print(f"Processing {len(image_paths)} images with context...")

        for i, image_path in enumerate(image_paths, 1):
            image_filename = Path(image_path).name
            print(f"[{i}/{len(image_paths)}] Processing: {image_filename}")

            # # Prune history to keep only the last 5 images in context before processing the current one
            # if self.chat is not None and hasattr(self.chat, 'history'):
            #     k = 5  # Keep last k images
            #     history_len = len(self.chat.history)
            #     if history_len >= 4:  # At least initial pair + one image pair
            #         num_images = (history_len - 2) // 2
            #         if num_images > k:
            #             # Remove the oldest image pair (user message with image + model response)
            #             del self.chat.history[2:4]

            max_retries = 3            
            for attempt in range(max_retries):
                try:
                    image = PIL.Image.open(image_path)
                    response = self.chat.send_message([f"{self.user_prompt}for this Screenshot {i}:", image])
                    
                    # if hasattr(response, 'usage_metadata') and response.usage_metadata:
                    #     print(f"Tokens used for image {i}: {response.usage_metadata}")


                    print("response.text ### ",response)

                    yaml_content = response.text
                    # Handle response with error checking
                    try:
                        yaml_content = response.text.strip()
                    except Exception:
                        yaml_content = self.handle_response_error(response, image_path)

                    # Clean response
                    # Clean markdown formatting
                    if yaml_content.startswith("```yaml"):
                        yaml_content = yaml_content.replace("```yaml", "").replace("```", "").strip()
                    elif yaml_content.startswith("```"):
                        yaml_content = yaml_content.replace("```", "").strip()

                    # Strip any leading backticks or junk
                    yaml_content = yaml_content.lstrip("` \n\r\t")

                    # If YAML is invalid, attempt a small retry within the same attempt
                    if not self._is_valid_yaml(yaml_content):
                        print("Returned YAML seems invalid. Attempting one more parse/clean pass...")
                        print(f"Raw content (first 200 chars): {repr(yaml_content[:200])}")
                        cleaned = yaml_content.replace("```yaml", "").replace("```", "").strip()
                        if self._is_valid_yaml(cleaned):
                            yaml_content = cleaned
                        else:
                            print(f"Cleaned content (first 200 chars): {repr(cleaned[:200])}")
                            raise ValueError("Model returned invalid YAML content")

                    # Generate output filename with frame ID
                    output_name = self.generate_output_filename(image_path, "element_extraction")
                    output_path = Path(output_folder) / output_name

                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(yaml_content)

                    # Update metadata with successful processing
                    # metadata[image_filename] = output_name
                    # self.save_metadata(metadata)

                    print(f"✓ Saved: {output_path}")
                    # print(f"✓ Updated metadata for: {image_filename}")
                    break  # Success, exit retry loop

                except Exception as e:
                    print(f"Attempt {attempt + 1}/{max_retries} failed: {e}")
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt
                        print(f"Waiting {wait_time} seconds before retry...")
                        time.sleep(wait_time)
                    else:
                        # All retries failed - halt the entire process
                        print(f"❌ All {max_retries} attempts failed for {image_filename}. Halting process...")
                        error_message = f"Processing failed after {max_retries} attempts on image {image_filename}: {str(e)}"
                        #to be removed for developing
                        winsound.Beep(440, 500)   # A4 note, 0.5 sec 
                        winsound.Beep(880, 500)   # A5 note, 0.5 sec
                        winsound.Beep(660, 1000)  # E5 note, 1 sec
                        return {"status": False, "error_stage": "image_processing", "error_message": error_message, "failed_image": image_filename, "processed_count": i-1}

            # Add delay between images to avoid rate limiting
            if i < len(image_paths):
                time.sleep(2)

            # Add longer delay after every 20 images
            if i % 20 == 0 and i < len(image_paths):
                print("⏳ Waiting 1 minute before next batch...")
                time.sleep(60)

        print("🎉 Processing complete!")

        # Trigger yamlDiff.py after completion
        print("📄 Triggering yamlDiff processor...")
        try:
            yaml_processor = YamlDiffProcessor()
            json_result = yaml_processor.process_frame_diffs(output_folder, isIntent=isIntent)

            if json_result:
                print("✅ yamlDiff processing completed successfully!")
                return json_result
            else:
                print("⚠️ yamlDiff processing completed but no result returned")
                return {"status": False, "error_stage": "yaml_diff", "error_message": "No result returned from yamlDiff"}

        except Exception as e:
            print(f"❌ Error running yamlDiff processor: {e}")
            import traceback
            traceback.print_exc()
            return {"status": False, "error_stage": "yaml_diff", "error_message": str(e)}

    # def process_images_with_context(self, image_paths: List[str], output_folder: str = "yaml_outputs", isIntent: str = None):
    #     """
    #     Process images using stateless calls for better reliability.
    #     """
    #     # Import YamlDiffProcessor here to avoid circular imports
    #     from yamlDiff import YamlDiffProcessor

    #     Path(output_folder).mkdir(exist_ok=True)
    #     print(f"Processing {len(image_paths)} images with stateless requests...")

    #     for i, image_path in enumerate(image_paths, 1):
    #         image_filename = Path(image_path).name
    #         print(f"[{i}/{len(image_paths)}] Processing: {image_filename}")

    #         # --- KEY CHANGE: Use the stateless analyze_screenshot method ---
    #         yaml_content = self.analyze_screenshot(image_path)

    #         # Check if the analysis resulted in an error
    #         if yaml_content.strip().startswith("# Error"):
    #             print(f"❌ Error analyzing {image_filename}. Halting process...")
    #             print(f"Error details: {yaml_content}")
    #             # Create a detailed error message for the return dictionary
    #             error_message = f"Processing failed on image {image_filename}. Details: {yaml_content}"
    #             return {"status": False, "error_stage": "image_processing", "error_message": error_message, "failed_image": image_filename, "processed_count": i-1}

    #         # Generate output filename with frame ID
    #         output_name = self.generate_output_filename(image_path, "element_extraction")
    #         output_path = Path(output_folder) / output_name

    #         with open(output_path, 'w', encoding='utf-8') as f:
    #             f.write(yaml_content)

    #         print(f"✓ Saved: {output_path}")

    #         # Add delay between images to avoid rate limiting
    #         if i < len(image_paths):
    #             time.sleep(2)

    #         # Add longer delay after every 20 images
    #         if i % 20 == 0 and i < len(image_paths):
    #             print("⏳ Waiting 1 minute before next batch...")
    #             time.sleep(60)

    #     print("🎉 Processing complete!")

    #     # Trigger yamlDiff.py after completion
    #     print("📄 Triggering yamlDiff processor...")
    #     try:
    #         yaml_processor = YamlDiffProcessor()
    #         json_result = yaml_processor.process_frame_diffs(output_folder, isIntent=isIntent)

    #         if json_result:
    #             print("✅ yamlDiff processing completed successfully!")
    #             return json_result
    #         else:
    #             print("⚠️ yamlDiff processing completed but no result returned")
    #             return {"status": False, "error_stage": "yaml_diff", "error_message": "No result returned from yamlDiff"}

    #     except Exception as e:
    #         print(f"❌ Error running yamlDiff processor: {e}")
    #         import traceback
    #         traceback.print_exc()
    #         return {"status": False, "error_stage": "yaml_diff", "error_message": str(e)}
    
    def process_images(self, image_paths: List[str], output_folder: str = "yaml_outputs"):
        """Process multiple images and save YAML files."""
        Path(output_folder).mkdir(exist_ok=True)

        print(f"Processing {len(image_paths)} images...")

        for i, image_path in enumerate(image_paths, 1):
            print(f"[{i}/{len(image_paths)}] Processing: {Path(image_path).name}")

            # Analyze screenshot with retry logic
            yaml_content = self.analyze_screenshot(image_path)

            # Generate output filename with frame ID
            output_name = self.generate_output_filename(image_path, "analysis")
            output_path = Path(output_folder) / output_name

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)

            if yaml_content.startswith("# Error"):
                print(f"❌ Error saved: {output_path}")
            else:
                print(f"✓ Saved: {output_path}")

            # Add delay between images
            if i < len(image_paths):
                time.sleep(2)

            # Add longer delay after every 20 images
            if i % 20 == 0 and i < len(image_paths):
                print("⏳ Waiting 1 minute before next batch...")
                time.sleep(60)

        print("🎉 Done!")

    def clear_context(self):
        """Clear the chat context and reset session."""
        self.chat = None
        print("Chat context cleared.")

    def auto_discover_images(self, folder_path: str, pattern: str = "*.jpg") -> List[str]:
        """
        Automatically discover all image files in a folder.

        Args:
            folder_path (str): Path to folder containing images
            pattern (str): File pattern to match (default: "*.jpg")

        Returns:
            List[str]: Sorted list of image file paths
        """
        import glob
        from pathlib import Path

        if not os.path.exists(folder_path):
            print(f"Folder not found: {folder_path}")
            return []

        # Find all matching files
        search_pattern = os.path.join(folder_path, pattern)
        image_paths = glob.glob(search_pattern)

        # Sort by filename to maintain order
        image_paths.sort()

        print(f"Found {len(image_paths)} images in {folder_path}")
        return image_paths

    def process_folder_with_context(self, input_folder: str, output_folder: str = None, pattern: str = "*.jpg", isIntent: str = None):
        """
        Process all images in a folder with context tracking.

        Args:
            input_folder (str): Folder containing images to process
            output_folder (str): Output folder for YAML files (defaults to input_folder + "_yaml_analysis")
            pattern (str): File pattern to match (default: "*.jpg")
        """
        # Auto-discover images
        # all_image_paths = self.auto_discover_images(input_folder, pattern)

        # if not all_image_paths:
        #     print("No images found to process!")
        #     return
        image_paths = self.auto_discover_images(input_folder, pattern)

        if not image_paths:
            print("No images found to process!")
            return
        # # Filter out already processed images
        # image_paths = self.filter_images_to_process(all_image_paths, input_folder)
        
        # if not image_paths:
        #     print("All images have already been processed!")
        #     return {"status": True, "message": "All images already processed", "processed_count": 0}

        # Set default output folder
        if output_folder is None:
            output_folder = f"{input_folder}_yaml_analysis"

        print("Input image paths:", len(image_paths), "images to process")

        # Process with context
        return self.process_images_with_context(image_paths, output_folder, isIntent)

    ## metadata.json file handling

    def load_metadata(self) -> dict:
        """Load existing metadata.json from root folder."""
        metadata_path = Path("metadata.json")
        if metadata_path.exists():
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Warning: Could not load metadata.json: {e}")
                return {}
        return {}

    def save_metadata(self, metadata: dict):
        """Save metadata.json to root folder."""
        metadata_path = Path("metadata.json")
        try:
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save metadata.json: {e}")

    def filter_images_to_process(self, image_paths: List[str], input_folder: str) -> List[str]:
        """Filter out images that have already been processed based on metadata.json."""
        metadata = self.load_metadata()
        
        if not metadata:
            print("No metadata.json found. Processing all images.")
            return image_paths
        
        # Get input folder name for comparison
        input_folder_name = Path(input_folder).name
        
        # Filter out already processed images
        images_to_process = []
        skipped_count = 0
        
        for image_path in image_paths:
            image_filename = Path(image_path).name
            
            # Check if this image from this input folder is already processed
            if image_filename in metadata:
                print(f"Skipping {image_filename} (already processed)")
                skipped_count += 1
            else:
                images_to_process.append(image_path)
        
        print(f"Skipped {skipped_count} already processed images.")
        print(f"Will process {len(images_to_process)} remaining images.")
        
        return images_to_process






if __name__ == "__main__":
    # analyzer = SimpleScreenshotAnalyzer()
     # Hardcoded defaults

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    # timestamp = 20250818_124537
    DEFAULT_INPUT_FOLDER = r"E:\loveable_AI\bunch\finalFrames_20250820_105708"
    DEFAULT_OUTPUT_FOLDER = f"ui_element_extraction_{timestamp}"
    DEFAULT_PATTERN = "*.jpg"
    DEFAULT_IS_INTENT = None

    parser = argparse.ArgumentParser(description="Process screenshots with Gemini model and generate YAML outputs.")
    parser.add_argument("-i", "--input_folder", help=f"Path to folder containing input images (default: {DEFAULT_INPUT_FOLDER})")
    parser.add_argument("-o", "--output_folder", help=f"Path to save YAML outputs (default: {DEFAULT_OUTPUT_FOLDER})")
    parser.add_argument("-p", "--pattern", help=f"File pattern to match (default: {DEFAULT_PATTERN})")
    parser.add_argument("--isIntent", help=f"Optional intent flag (default: {DEFAULT_IS_INTENT})")

    args = parser.parse_args()

    # Use CLI values if provided, otherwise fall back to defaults
    input_folder = args.input_folder or DEFAULT_INPUT_FOLDER
    output_folder = args.output_folder or DEFAULT_OUTPUT_FOLDER
    pattern = args.pattern or DEFAULT_PATTERN
    isIntent = args.isIntent or DEFAULT_IS_INTENT

    analyzer = SimpleScreenshotAnalyzer()
    result = analyzer.process_folder_with_context(
        input_folder=input_folder,
        output_folder=output_folder,
        pattern=pattern,
        isIntent=isIntent
    )

    # all_image_paths = analyzer.auto_discover_images(input_folder, pattern)

    # result = analyzer.process_images(
    #     image_paths=all_image_paths,
    #     output_folder=output_folder,
    # )

    print("\n=== Process Result ===")
    print(result)