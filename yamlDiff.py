import yaml
import os
import glob
import re
import sys
from deepdiff import DeepDiff
import shutil
import asyncio 
import time

class YamlDiffProcessor:
    """Minimal class to handle YAML diff processing operations without aggressive cleaning."""
    
    def __init__(self):
        """Initialize the YAML diff processor."""
        pass

    def load_yaml_file(self, path):
        """
        Load a YAML file and return its content WITHOUT any character cleaning.
        
        Args:
            path: File path
        """
        # Try different encodings to handle various file formats
        encodings_to_try = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
        
        data = None
        for encoding in encodings_to_try:
            try:
                with open(path, 'r', encoding=encoding) as file:
                    data = yaml.safe_load(file)
                print(f"Successfully loaded {path} with encoding: {encoding}")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"Error with encoding {encoding}: {e}")
                continue
        
        if data is None:
            raise RuntimeError(f"Could not load file {path} with any supported encoding")
            
        # NO CHARACTER CLEANING - return raw data
        return data

    def save_yaml_file(self, data, path):
        """Save data to a YAML file."""
        with open(path, 'w', encoding='utf-8') as file:
            yaml.dump(data, file, allow_unicode=True, sort_keys=False, default_flow_style=False)

    def extract_frame_id_from_filename(self, filename):
        """
        Extract frame ID from filename patterns like:
        - ui_elements_0000.yaml -> returns "0000"
        - ui_elements_0001.yaml -> returns "0001"
        """
        # Pattern to match ui_elements_XXXX.yaml
        pattern = r'ui_elements_(\d{4})\.yaml'
        match = re.search(pattern, filename)
        if match:
            return match.group(1)  # Return the frame ID part

        # Fallback for old format
        pattern_old = r'frame_(\d{4})_.*\.yaml'
        match_old = re.search(pattern_old, filename)
        if match_old:
            return match_old.group(1)

        return "0000"  # Default frame ID

    def extract_frame_info_from_filename(self, filename):
        """
        Extract frame ID and timestamp from filename patterns like:
        - ui_elements_0003_002.yaml -> returns ("0003", "002")
        - ui_elements_0004_004.yaml -> returns ("0004", "004")
        - ui_elements_0000.yaml -> returns ("0000", None)
        """
        # Pattern to match ui_elements_XXXX_YYY.yaml (with timestamp)
        pattern_with_timestamp = r'ui_elements_(\d{4})_(\d{3})\.yaml'
        match = re.search(pattern_with_timestamp, filename)
        if match:
            return match.group(1), match.group(2)  # Return frame ID and timestamp

        # Pattern to match ui_elements_XXXX.yaml (without timestamp)
        pattern_without_timestamp = r'ui_elements_(\d{4})\.yaml'
        match = re.search(pattern_without_timestamp, filename)
        if match:
            return match.group(1), None  # Return frame ID only

        return "0000", None  # Default frame ID

    def compare_yaml(self, file1, file2, output_diff):
        """Compare two YAML files and save the differences using your working approach."""
        print(f"\n🔍 Comparing files:")
        print(f"  File 1: {file1}")
        print(f"  File 2: {file2}")
        
        # Load files WITHOUT any character cleaning
        yaml1 = self.load_yaml_file(file1)
        yaml2 = self.load_yaml_file(file2)
        
        # DEBUG: Check if browser_component exists in both files
        if 'browser_component' in yaml1:
            print(f"  📋 File 1 browser_component: {yaml1['browser_component']}")
        else:
            print("  ⚠️ File 1 missing browser_component")
            
        if 'browser_component' in yaml2:
            print(f"  📋 File 2 browser_component: {yaml2['browser_component']}")
        else:
            print("  ⚠️ File 2 missing browser_component")
        
        # Use DeepDiff with the EXACT same settings as your working test
        diff = DeepDiff(yaml1, yaml2, ignore_order=True, verbose_level=2)
        
        # DEBUG: Print what DeepDiff found
        print(f"  🔍 DeepDiff found {len(diff)} difference categories:")
        for key in diff.keys():
            diff_count = len(diff[key]) if hasattr(diff[key], '__len__') and not isinstance(diff[key], str) else 'present'
            print(f"    - {key}: {diff_count}")
            
            # Show browser_component changes if found
            if 'browser_component' in str(diff[key]):
                print(f"      ✅ Contains browser_component changes!")
        
        # Convert to dict (this is what your test code essentially does)
        diff_dict = diff.to_dict() if hasattr(diff, 'to_dict') else dict(diff)
        
        # Save the diff as YAML
        self.save_yaml_file(diff_dict, output_diff)
        print(f"✅ Differences saved in '{output_diff}'")
        
        # VERIFICATION: Check the saved diff contains browser changes
        try:
            with open(output_diff, 'r', encoding='utf-8') as f:
                saved_content = f.read()
            
            if 'browser_component' in saved_content:
                print("  ✅ Verified: browser_component changes saved to diff file")
            else:
                print("  ❌ Warning: browser_component changes not found in saved diff")
        except Exception as e:
            print(f"  ⚠️ Could not verify saved diff: {e}")

    async def process_frame_diffs_async(self, yaml_folder: str = "yaml_outputs", output_filename: str = None):
        """Async version of process_frame_diffs"""
        import asyncio
        loop = asyncio.get_event_loop()
        
        return await loop.run_in_executor(
            None,
            self.process_frame_diffs,
            yaml_folder,
            output_filename
        )

    def process_frame_diffs(self, input_folder, frame_pattern="ui_elements_*.yaml", isIntent=None):
        """Process all frame files sequentially and create diffs between consecutive frames"""
        # Create diff_folder if it doesn't exist
        diff_folder = os.path.join(input_folder, "diff_folder")
        os.makedirs(diff_folder, exist_ok=True)
        
        # Find all frame files and sort them
        frame_files = glob.glob(os.path.join(input_folder, frame_pattern))
        frame_files.sort(key=lambda x: int(''.join(filter(str.isdigit, os.path.basename(x)))))
        
        if len(frame_files) < 2:
            print(f"Found only {len(frame_files)} frame files. Need at least 2 files to create diffs.")
            return None
        
        print(f"🔄 Creating diff folder structure...")
        
        # Step 1: Clear diff folder to ensure clean state
        for existing_file in os.listdir(diff_folder):
            file_path = os.path.join(diff_folder, existing_file)
            if os.path.isfile(file_path):
                os.remove(file_path)
        
        # Step 2: Process consecutive frame pairs FIRST with early timestamps
        base_time = time.time()
        for i in range(len(frame_files) - 1):
            frame1_path = frame_files[i]
            frame2_path = frame_files[i + 1]

            # Extract frame info from both files
            frame1_name = os.path.basename(frame1_path)
            frame2_name = os.path.basename(frame2_path)

            frame1_id, frame1_timestamp = self.extract_frame_info_from_filename(frame1_name)
            frame2_id, frame2_timestamp = self.extract_frame_info_from_filename(frame2_name)

            # Generate diff filename with frame IDs and timestamps
            if frame1_timestamp and frame2_timestamp:
                diff_filename = f"ui_diff_{frame1_id}_{frame1_timestamp}_to_{frame2_id}_{frame2_timestamp}.yaml"
            else:
                # Fallback to old format if timestamps not available
                diff_filename = f"ui_diff_{frame1_id}_to_{frame2_id}.yaml"

            diff_output_path = os.path.join(diff_folder, diff_filename)

            print(f"\n🔄 Processing diff {i+1}: {frame1_name} vs {frame2_name}")
            print(f"Output filename: {diff_filename}")

            try:
                self.compare_yaml(frame1_path, frame2_path, diff_output_path)

                # Set incremental timestamp for proper ordering (early timestamps)
                file_timestamp = base_time + i
                os.utime(diff_output_path, (file_timestamp, file_timestamp))

            except Exception as e:
                print(f"❌ Error processing files: {str(e)}")
                raise RuntimeError(f"yaml_diff_failure: {e}")
        
        # Step 3: Create ui_diff_0000_to_0000.yaml LAST with latest timestamp
        first_file_path = frame_files[0]
        special_file_dest = os.path.join(diff_folder, "ui_diff_0000_to_0000.yaml")
        
        # Copy the first frame file as ui_diff_0000_to_0000.yaml
        shutil.copy2(first_file_path, special_file_dest)
        
        # Set timestamp to be the latest to ensure it sorts last
        latest_timestamp = base_time + len(frame_files) + 100  # Much later than diff files
        os.utime(special_file_dest, (latest_timestamp, latest_timestamp))
        print(f"✅ Created ui_diff_0000_to_0000.yaml with latest timestamp (positioned last)")
        
        # Step 4: Verify file ordering
        print(f"\n✅ All processing complete! Verifying file order in '{diff_folder}'...")
        
        # Get files sorted by name (which should match our intended order)
        all_files = sorted(os.listdir(diff_folder))
        
        # Verify ui_diff_0000_to_0000.yaml is last
        if all_files and all_files[-1] == "ui_diff_0000_to_0000.yaml":
            print("✅ ui_diff_0000_to_0000.yaml is correctly positioned last!")
        else:
            print("⚠️ ui_diff_0000_to_0000.yaml ordering may need adjustment")
            
        print("📄 Final file order:")
        for idx, filename in enumerate(all_files):
            file_path = os.path.join(diff_folder, filename)
            file_stat = os.stat(file_path)
            mod_time = time.ctime(file_stat.st_mtime)
            print(f"  {idx+1:2d}. {filename} (modified: {mod_time})")
        
        # Trigger context generator
        result = self.trigger_context_generator(diff_folder, isIntent)
        return result

    def trigger_context_generator(self, diff_folder_path, isIntent=None):
        """
        Trigger the context_generator.py using class-based approach
        Returns the JSON result from context_generator
        isIntent: Optional flag to indicate if we should only process intent
        """
        try:
            # Import and use context generator as a class
            from context_generator import ContextGenerator  # Assuming it exists as a class
            
            print(f"\n{'='*50}")
            print("Triggering context generator...")
            print('='*50)

            context_gen = ContextGenerator()
            
            # Create context generator instance and process
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            json_result = loop.run_until_complete(
                context_gen.main(diff_folder_path, isIntent)
            )
            loop.close()
            
            if json_result:
                print("✅ Context generator completed successfully!")
                return json_result
            else:
                print("⚠️ Context generator completed but returned no result")
                return None
                
        except ImportError:
            print("❌ context_generator.py not found or doesn't have ContextGenerator class")
            return None
        except Exception as e:
            print(f"❌ Error running context generator: {str(e)}")
            import traceback
            traceback.print_exc()
            return None


# === Standalone Usage ===
if __name__ == "__main__":
    try:
        print("\n" + "="*60 + "\n")
        
        if len(sys.argv) > 1:
            input_folder_path = sys.argv[1]
            print(f"🔄 Processing folder from command line: {input_folder_path}")
        else:
            # Fallback to hardcoded path if no argument provided
            input_folder_path = r"E:\loveable_AI\bunch\output_genai"
            print(f"🔄 Using default folder: {input_folder_path}")
        
        # Verify folder exists
        if not os.path.exists(input_folder_path):
            print(f"❌ Error: Folder '{input_folder_path}' does not exist!")
            sys.exit(1)
        
        print("\n" + "="*60 + "\n")
        
        # Try different patterns to see which one matches your files
        patterns_to_try = [
            "ui_elements_*.yaml",               # New frame-based pattern
            "frame_*_*_context_analysis.yaml", # Old timestamped pattern
            "frame_*_context_analysis.yaml",   # Original pattern
            "frame_*.yaml",                     # Simpler pattern
            "*analysis.yaml"                    # Even broader pattern
        ]

        print("Testing different file patterns:")
        for pattern in patterns_to_try:
            test_files = glob.glob(os.path.join(input_folder_path, pattern))
            print(f"Pattern '{pattern}': {len(test_files)} files found")
            if test_files:
                print(f"  Sample files: {[os.path.basename(f) for f in test_files[:3]]}")
        
        # Use the new frame-based pattern
        file_pattern = "ui_elements_*.yaml"  # Updated for frame-based files
        
        print(f"\n{'='*50}")
        print("Starting processing with pattern:", file_pattern)
        print('='*50)
        
        # Create processor instance and run
        processor = YamlDiffProcessor()

        # NO CHARACTER CLEANING AT ALL - this was the source of the problem
        json_result = processor.process_frame_diffs(input_folder_path, file_pattern)
        
        # Output the JSON result
        if json_result:
            import json
            print(f"\n{'='*50}")
            print("FINAL JSON RESULT:")
            print('='*50)
            print(json.dumps(json_result, indent=2))
        else:
            print("No JSON result received.")
            
    except Exception as e:
        print(f"CRITICAL ERROR in yamlDiff.py: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)