
import os
import pathlib
import openai
from PIL import Image  # Retained if needed elsewhere, but not used for encoding
import asyncio
import aiofiles
import time
from datetime import datetime
import json
import base64


# --- Configuration ---
OPENAI_API_KEY = 'sk-your-openai-api-key-here'  # Replace with your actual OpenAI API key
IMAGE_FOLDER_PATH = r'E:\loveable_AI\bunch\finalFrames_20250820_232550 copy'


PROMPT = """

You are an expert UI analyst. Your task is to deconstruct a given screenshot (frame) of a web application and generate a structured representation of its components in valid YAML format.



### **1. Core Rules**

1.  **Classify the frame into two primary regions:**
    * `browser_component`: This region contains the browser's chrome, specifically the active tab title and the URL in the address bar.
    * `webpage`: This is the main viewport containing the rendered website content.

2.  **Extract Browser Information:**
    * From `browser_component`, extract the `tab_title` and the `url`.

3.  **Deconstruct the Webpage:**
    * Identify and classify all visible subcomponents within the `webpage` region into one of the following types:
        * `menu/navigation_bar`: Top or side navigation menus.
        * `header/banner`: Logos, branding, or site-wide messages.
        * `workspace`: The main interactive area containing forms, tables, media, etc.
        * `sidebar/drawer`: Collapsible panels, filters, or secondary menus.
        * `footer`: Bottom section with copyright, links, contact info.
        * `modal/popup`: Overlays, dialogs, or confirmation boxes.
        * `notifications/toasts`: Temporary system messages.

    * For each subcomponent, extract all visible **UI elements** (buttons, inputs, links, etc.). Represent each element in YAML with the following attributes:
        * `type`: The type of element (e.g., `button`, `text_field`, `dropdown`, `table`, `icon`).
        * `label`: The visible text or accessible name of the element.
        * `id`: A unique, generated identifier (e.g., "btn_submit", "input_email_1").
        * `position`: The `{x, y}` coordinates of the top-left corner relative to the webpage viewport.
        * `size`: The `{width, height}` of the element in pixels.
        * `elements_within` (optional): For container elements (like a navigation bar or actions cell), list the nested elements.
        * `state` (optional): The current state if visually distinct (e.g., `hovered`, `active`, `disabled`, `selected`, `focused`, `clicked`).
        * `value` (for inputs only, optional): The visible text or value entered/pasted inside the input field.
        * `interaction` (optional): Explicit user actions if visually detectable, such as:
            - `typed` (user typed text into input)
            - `pasted` (user pasted text into input)
            - `clicked` (user clicked a button/icon)
            - `focused` (element currently active)

---

### **2. Special Handling for Tables**

If a `table` is detected within the `workspace`, it must be structured as follows:
* `type: table`
* `headers`: A list of the column header strings.
* `rows`: A list of row objects.
    * Each row must have an `id` (e.g., "row_1").
    * Each row contains a list of `cells`.
    * Each cell can be a simple text element `{type: text, label: "...", id: "..."}`.
    * If a cell contains action icons or buttons (e.g., edit, delete, view), its `type` must be `actions` and it must contain an `elements_within` list detailing each clickable element (icon, button, etc.).

---

### **3. Error Handling**

**This is a critical rule.** If the provided image **cannot be analyzed**—because it is blurry, corrupted, not a web application screenshot, or otherwise unparsable—you MUST return the following specific YAML structure. Do not attempt to guess the content.

```yaml
error:
  type: "ANALYSIS_FAILED"
  message: "The provided image could not be analyzed. It may be blurry, corrupted, or not a valid web application screenshot."
````

---

### **4. Examples**

#### \*\* Success Case Example (with interactions)\*\*

```yaml
browser_component:
  tab_title: "Login - Example"
  url: "https://example.com/login"
webpage:
  workspace:
    - type: text_field
      label: "Email"
      id: "input_email_1"
      position: { x: 100, y: 200 }
      size: { width: 300, height: 40 }
      state: focused
      value: "<EMAIL>"
      interaction: pasted

    - type: button
      label: "Sign In"
      id: "btn_signin_1"
      position: { x: 100, y: 260 }
      size: { width: 100, height: 40 }
      state: clicked
      interaction: clicked
```

#### \*\* Error Case Example\*\*

```yaml
error:
  type: "ANALYSIS_FAILED"
  message: "The provided image could not be analyzed. It may be blurry, corrupted, or not a valid web application screenshot."
```

---

### **CRITICAL BROWSER DETECTION RULES:**

* Pay special attention to browser chrome (tabs, address bar, navigation buttons)
* Always capture tab titles, URLs, and tab states
* Detect if address bar is focused or being edited
* Count total visible tabs

**Rules:**

* Treat each input as a browser screenshot (frame)
* Extract information into TWO sections: `browser_component` and `webpage`
* For browser\_component, MUST capture:

  * tab\_title: Current active tab title
  * url: Complete URL with protocol
  * total\_tabs: Number of visible tabs
  * address\_bar\_focused: true/false if address bar is selected/being edited
  * address\_bar\_content: What text is visible in address bar (if different from URL)
  * all\_visible\_tabs: List of all visible tab titles (if multiple tabs shown)
* Output must be valid YAML only — no explanations, no extra text
* If data is missing or not visible, set the value to `null`
* Capture all visible details in the screenshot UI
* Represent the UI in a hierarchical tree structure using `component_type` and `subcomponents`

### **5. Final Instruction**

Analyze the provided frame and generate ONLY the valid YAML output based on the rules and structure defined above.
Do not include any explanations, apologies, or conversational text in your response.

Important Restriction:
Do not produce any non-ASCII or Unicode characters (including emojis, special symbols, or characters outside the standard English keyboard).
Only plain ASCII text is allowed in the YAML output.

"""


SYSTEM_PROMPT = """You are an expert in analyzing browser screenshots. 


Always follow the user instructions exactly. 


Rules:
- Treat each input as a browser screenshot (frame).  
- Extract information into two sections only: `browser_component` and `webpage`.  
- Output must be valid YAML only — no explanations, no extra text.  
- If data is missing or not visible, set the value to `null`.  
- Capture all visible details in the screenshot UI.  
- Represent the UI in a hierarchical tree structure using `component_type` and `subcomponents`.

SPECIAL FOCUS: Pay extra attention to:

    Browser tab titles and which tab is currently active
    The URL in the address bar
    Whether user is typing in the address bar
    Total number of browser tabs visible
    Any new tab buttons or tab switching indicators

    Include all browser navigation elements in the browser_component section.



 """



# --- Code ---
HISTORY_FILE = "chat_history_openai.json"
METADATA_FILE = "metadata_openai.json"


def configure_openai():
    """Configures the OpenAI client with the API key."""
    try:
        openai.api_key = OPENAI_API_KEY
    except Exception as e:
        print(f"Error configuring OpenAI: {e}")
        print("Please ensure you have set a valid OPENAI_API_KEY.")
        exit()
        

def setup_output_folder():
    """Creates a new timestamped directory for the output YAML files."""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir_name = f"today_output_{timestamp}"
    output_path = pathlib.Path(output_dir_name)
    output_path.mkdir(parents=True, exist_ok=True)
    print(f"Created output directory: {output_path}")
    return output_path


async def save_history(messages):
    """Saves the chat history to a JSON file asynchronously."""
    serializable_history = [
        {"role": message["role"], "parts": [message["content"]]}
        for message in messages if message["role"] != "system"
    ]
    async with aiofiles.open(HISTORY_FILE, 'w') as f:
        await f.write(json.dumps(serializable_history, indent=2))
    print("Chat history saved.")

async def load_history():
    """Loads the last 20 chat history objects with non-empty parts from a JSON file asynchronously."""
    if os.path.exists(HISTORY_FILE):
        async with aiofiles.open(HISTORY_FILE, 'r') as f:
            content = await f.read()
            all_history = json.loads(content)
        
        # Filter objects where parts is not empty and contains non-empty strings
        filtered_history = [
            item for item in all_history 
            if item.get("parts") and 
            any(part.strip() for part in item["parts"] if isinstance(part, str))
        ]
        
        # Get the last 20 objects
        recent_history = filtered_history[-20:] if len(filtered_history) > 20 else filtered_history
        
        print(f"Chat history loaded: {len(recent_history)} objects from last 20 with non-empty parts.")
        return recent_history
    return []

async def load_metadata():
    """Loads the metadata from a JSON file asynchronously."""
    if os.path.exists(METADATA_FILE):
        async with aiofiles.open(METADATA_FILE, 'r') as f:
            content = await f.read()
            return json.loads(content)
    return {}

async def save_metadata(metadata):
    """Saves the metadata to a JSON file asynchronously."""
    async with aiofiles.open(METADATA_FILE, 'w') as f:
        await f.write(json.dumps(metadata, indent=2))


def base64_encode_image(image_path):
    """Encodes the image to base64."""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def generate_yaml_from_image(client, messages, image_path, prompt_text, max_retries=4):
    """
    Analyzes a single image using the OpenAI model with a retry mechanism.
    Raises an exception if all retries fail.
    """
    print(f"-> Processing image: {image_path.name}")
    retries = 0
    while retries < max_retries:
        try:
            base64_image = base64_encode_image(image_path)
            
            user_message = {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt_text},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                ]
            }
            
            temp_messages = messages + [user_message]
            
            response = client.chat.completions.create(
                model='gpt-4.1',
                messages=temp_messages,
                temperature=0.1,
                top_p=0.3
            )
            print("response.choices[0].message.content ### ", response.choices[0].message.content)
            
            cleaned_response = response.choices[0].message.content.strip()
            if cleaned_response.startswith("```yaml"):
                cleaned_response = cleaned_response[7:].strip()
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3].strip()
            
            # Append text-only user message and assistant response to history
            # messages.append({"role": "user", "content": prompt_text})
            # messages.append({"role": "assistant", "content": cleaned_response})
            
            return cleaned_response
            
        except FileNotFoundError:
            raise FileNotFoundError(f"Error: Image file not found at {image_path}")
        except Exception as e:
            retries += 1
            print(f"Attempt {retries}/{max_retries} failed for {image_path.name}. Error: {e}")
            if retries < max_retries:
                wait_time = 2 ** retries
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)  # Use time.sleep for synchronous wait
            else:
                # Raise an exception to stop the process
                raise Exception(f"Failed to process {image_path.name} after {max_retries} attempts.")


async def process_single_image(client, messages, image_path, prompt, output_folder, metadata, semaphore):
    """Process a single image with concurrency control."""
    async with semaphore:
        image_name = image_path.name
        
        if image_name in metadata:
            print(f"Skipping {image_name}, already processed according to metadata.json.")
            return metadata
        
        try:
            yaml_output = generate_yaml_from_image(client, messages, image_path, prompt)
            
            # Extract frame ID and timestamp from filename
            input_name = image_path.stem
            # Pattern to match frame_XXXX_YYY (with timestamp)
            import re
            pattern_with_timestamp = r'frame_(\d{4})_(\d{3})'
            match = re.search(pattern_with_timestamp, input_name)
            if match:
                frame_id, timestamp = match.group(1), match.group(2)
                output_filename = f"ui_elements_{frame_id}_{timestamp}.yaml"
            else:
                # Fallback to old logic for backward compatibility
                numeric_part = image_path.stem.split('_')[-1]
                output_filename = f"ui_elements_{numeric_part}.yaml"
            output_file_path = output_folder / output_filename
            
            # Write YAML output asynchronously
            async with aiofiles.open(output_file_path, 'w') as f:
                await f.write(yaml_output)


            # Update metadata
            metadata[image_name] = output_filename
            await save_metadata(metadata)


            print("-" * 50)
            print(f"File: {image_name}")
            print(f"Generated YAML written to: {output_file_path}")
            print("Generated YAML:")
            print(yaml_output)
            print("-" * 50 + "\n")
            
            await save_history(messages)
            
            return metadata
            
        except Exception as e:
            # If an exception is raised, print the error and re-raise
            print("-" * 50)
            print(f"An unrecoverable error occurred processing {image_name}: {e}")
            print("-" * 50)
            raise


async def process_all_images_in_folder(folder_path, prompt, output_folder, system_prompt, max_concurrent=1):
    """
    Iterates through all images in a folder, generates YAML asynchronously.
    max_concurrent: Maximum number of images to process simultaneously.
    """
    image_dir = pathlib.Path(folder_path)
    if not image_dir.is_dir():
        print(f"Error: The specified folder '{folder_path}' does not exist.")
        return


    image_extensions = ['.jpg']
    image_files = sorted([
        file for file in image_dir.iterdir() 
        if file.suffix.lower() in image_extensions
    ])


    if not image_files:
        print(f"No images found in the folder: {folder_path}")
        return


    print(f"Found {len(image_files)} image(s) to process.\n")
    
    metadata = await load_metadata()
    history = await load_history()
    
    client = openai.OpenAI()
    
    messages = [{"role": "system", "content": system_prompt}]
    for h in history:
        role = "user" if h["role"] == "user" else "assistant"
        content = "\n".join(h["parts"])
        if content.strip():
            messages.append({"role": role, "content": content})
    
    if metadata:
        print("Existing metadata found. Resuming from last state.")
    else:
        print("No existing metadata found. Starting a new session.")


    # Create semaphore to limit concurrent processing
    semaphore = asyncio.Semaphore(max_concurrent)
    
    # Create tasks for all images
    tasks = []
    for image_path in image_files:
        task = process_single_image(client, messages, image_path, prompt, output_folder, metadata, semaphore)
        tasks.append(task)
    
    try:
        # Process all images concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check for exceptions in results
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Error processing {image_files[i].name}: {result}")
                print("Processing stopped due to error.")
                return
                
        print("All images processed successfully!")
        
    except Exception as e:
        print(f"An unrecoverable error occurred: {e}")
        print("Processing stopped.")
        return


async def main():
    """Main async function."""
    configure_openai()
    # output_dir = setup_output_folder()
    output_dir = r'E:\loveable_AI\bunch\Normal_Yamls_4_openai'
    
    # Ensure output_dir is a Path object
    if isinstance(output_dir, str):
        output_dir = pathlib.Path(output_dir)
    
    await process_all_images_in_folder(IMAGE_FOLDER_PATH, PROMPT, output_dir, SYSTEM_PROMPT)
    print("Processing complete.")


if __name__ == "__main__":
    asyncio.run(main())
