[{"image_name": "frame_0000.jpg", "error": "\n  No API_KEY or ADC found. Please either:\n    - Set the `GOOGLE_API_KEY` environment variable.\n    - Manually pass the key with `genai.configure(api_key=my_api_key)`.\n    - Or set up Application Default Credentials, see https://ai.google.dev/gemini-api/docs/oauth for more information.", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-27T22:37:57.683965", "attempt": 1}, {"image_name": "frame_0000.jpg", "error": "\n  No API_KEY or ADC found. Please either:\n    - Set the `GOOGLE_API_KEY` environment variable.\n    - Manually pass the key with `genai.configure(api_key=my_api_key)`.\n    - Or set up Application Default Credentials, see https://ai.google.dev/gemini-api/docs/oauth for more information.", "tokens": null, "wait_time": 60, "api_key_rotation": false, "timestamp": "2025-08-27T22:38:39.944852", "attempt": 2}, {"image_name": "frame_0000.jpg", "error": "\n  No API_KEY or ADC found. Please either:\n    - Set the `GOOGLE_API_KEY` environment variable.\n    - Manually pass the key with `genai.configure(api_key=my_api_key)`.\n    - Or set up Application Default Credentials, see https://ai.google.dev/gemini-api/docs/oauth for more information.", "tokens": null, "wait_time": 90, "api_key_rotation": false, "timestamp": "2025-08-27T22:39:52.278773", "attempt": 3}, {"image_name": "frame_0000.jpg", "error": "\n  No API_KEY or ADC found. Please either:\n    - Set the `GOOGLE_API_KEY` environment variable.\n    - Manually pass the key with `genai.configure(api_key=my_api_key)`.\n    - Or set up Application Default Credentials, see https://ai.google.dev/gemini-api/docs/oauth for more information.", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-27T22:41:52.297445", "attempt": 4}, {"image_name": "frame_0000.jpg", "error": "object GenerateContentResponse can't be used in 'await' expression", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-27T22:43:49.450969", "attempt": 5}, {"image_name": "frame_0000.jpg", "success": "Able to generate UI elements", "tokens": 1961, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T22:44:41.842731", "attempt": 6}, {"image_name": "frame_0001.jpg", "success": "Able to generate UI elements", "tokens": 2706, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T22:44:52.509765", "attempt": 1}, {"image_name": "frame_0002.jpg", "success": "Able to generate UI elements", "tokens": 3451, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T22:45:00.931347", "attempt": 1}, {"image_name": "frame_0003.jpg", "success": "Able to generate UI elements", "tokens": 4196, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-27T22:45:17.328772", "attempt": 1}, {"image_name": "frame_0004.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 26\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-27T22:45:33.695234", "attempt": 1}, {"image_name": "frame_0004.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 55\n}\n]", "tokens": null, "wait_time": 60, "api_key_rotation": false, "timestamp": "2025-08-27T22:46:04.963700", "attempt": 2}, {"image_name": "frame_0004.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 43\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-28T15:01:16.183218", "attempt": 3}, {"image_name": "frame_0004.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 57\n}\n]", "tokens": null, "wait_time": 60, "api_key_rotation": false, "timestamp": "2025-08-28T15:02:01.553896", "attempt": 4}, {"image_name": "frame_0004.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 56\n}\n]", "tokens": null, "wait_time": 90, "api_key_rotation": false, "timestamp": "2025-08-28T15:03:02.848886", "attempt": 5}, {"image_name": "frame_0004.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 59\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-08-28T15:04:59.501570", "attempt": 6}, {"image_name": "frame_0004.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 42\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-28T15:05:17.256300", "attempt": 7}, {"image_name": "frame_0004.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-08-28T15:05:51.355820", "attempt": 8}, {"image_name": "frame_0004.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-28T15:06:08.457395", "attempt": 9}, {"image_name": "frame_0004.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-08-28T15:06:40.562558", "attempt": 10}, {"image_name": "frame_0004.jpg", "success": "Able to generate UI elements", "tokens": 5660, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T15:08:33.485328", "attempt": 11}, {"image_name": "frame_0005.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-08-28T15:08:35.826952", "attempt": 1}, {"image_name": "frame_0005.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-28T15:08:52.755607", "attempt": 2}, {"image_name": "frame_0005.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-08-28T15:09:24.654483", "attempt": 3}, {"image_name": "frame_0027.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-08-28T17:34:26.035509", "attempt": 1}, {"image_name": "frame_0027.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-28T17:34:43.326844", "attempt": 2}, {"image_name": "frame_0027.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-08-28T17:35:15.526326", "attempt": 3}, {"image_name": "frame_0027.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-08-28T17:39:17.703183", "attempt": 4}, {"image_name": "frame_0027.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-08-28T17:39:34.940969", "attempt": 5}, {"image_name": "frame_0027.jpg", "error": "500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-08-28T17:40:07.151611", "attempt": 6}, {"image_name": "frame_0027.jpg", "success": "Able to generate UI elements", "tokens": 12563, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T17:50:07.356919", "attempt": 7}, {"image_name": "frame_0028.jpg", "error": "Invalid operation: The `response.text` quick accessor requires the response to contain a valid `Part`, but none were returned. The candidate's [finish_reason](https://ai.google.dev/api/generate-content#finishreason) is 1.", "tokens": 16060, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-08-28T17:50:20.932459", "attempt": 1}, {"image_name": "frame_0028.jpg", "success": "Able to generate UI elements", "tokens": 16060, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T17:51:14.880704", "attempt": 2}, {"image_name": "frame_0029.jpg", "error": "Generated content is not valid YAML. Error: while parsing a flow mapping\n  in \"<unicode string>\", line 45, column 19:\n              bounds: {x: 11\n                      ^\nexpected ',' or '}', but got '<stream end>'\n  in \"<unicode string>\", line 45, column 25:\n              bounds: {x: 11\n                            ^", "tokens": 19696, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-08-28T17:51:26.056568", "attempt": 1}, {"image_name": "frame_0029.jpg", "success": "Able to generate UI elements", "tokens": 19696, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T17:52:02.277101", "attempt": 2}, {"image_name": "frame_0030.jpg", "success": "Able to generate UI elements", "tokens": 23267, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T17:52:08.374089", "attempt": 1}, {"image_name": "frame_0000_000.jpg", "success": "Able to generate UI elements", "tokens": 1978, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:29:59.591890", "attempt": 1}, {"image_name": "frame_0001_000.jpg", "success": "Able to generate UI elements", "tokens": 5250, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:30:31.500074", "attempt": 1}, {"image_name": "frame_0002_001.jpg", "success": "Able to generate UI elements", "tokens": 8522, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:31:07.241453", "attempt": 1}, {"image_name": "frame_0003_002.jpg", "success": "Able to generate UI elements", "tokens": 11794, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:40:37.839964", "attempt": 1}, {"image_name": "frame_0004_003.jpg", "success": "Able to generate UI elements", "tokens": 15066, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:41:08.908379", "attempt": 1}, {"image_name": "frame_0005_004.jpg", "success": "Able to generate UI elements", "tokens": 18338, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:41:27.411047", "attempt": 1}, {"image_name": "frame_0006_005.jpg", "success": "Able to generate UI elements", "tokens": 21610, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:41:56.177018", "attempt": 1}, {"image_name": "frame_0007_006.jpg", "success": "Able to generate UI elements", "tokens": 24882, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:42:27.909793", "attempt": 1}, {"image_name": "frame_0008_007.jpg", "success": "Able to generate UI elements", "tokens": 28154, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:42:58.939949", "attempt": 1}, {"image_name": "frame_0009_007.jpg", "success": "Able to generate UI elements", "tokens": 31426, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-08-28T22:43:16.492121", "attempt": 1}]