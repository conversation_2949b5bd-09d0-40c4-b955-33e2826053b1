import os
import pathlib
import google.generativeai as genai
from PIL import Image
import asyncio
import aiofiles
import time
from datetime import datetime
from google.generativeai.types import HarmCategory, HarmBlockThreshold
import json
from google.generativeai.types import GenerationConfig

# --- Configuration ---
GOOGLE_API_KEY = 'AIzaSyBTdGX8zJNaCPU8YgaEE_OQJHhzbeg2Wd8' 
IMAGE_FOLDER_PATH = r'E:\loveable_AI\bunch\finalFrames_20250820_232550 copy'

PROMPT = """

```

# UI Screenshot Analysis Prompt (Improved Version)

You are an expert UI analyst. Your task is to analyze a web application screenshot and generate a structured YAML representation of its components.

## **Core Requirements**

### **1. Primary Classification**
Classify the screenshot into two main regions:
- `browser_component`: Browser chrome (tabs, address bar, navigation buttons)
- `webpage`: Main content viewport

### **2. Browser Component Extraction**
From the browser chrome, extract only:
- `tab_title`: Active tab title text - EXACT text without any modifications, abbreviations, or corrections
- `url`: Complete URL visible in address bar - EXACT characters including all parameters, fragments, and special characters
- `address_bar_focused`: Boolean indicating if address bar is active/selected

**CRITICAL ACCURACY REQUIREMENT:**
All text extraction must be CHARACTER-PERFECT. Do not:
- Fix spelling errors
- Abbreviate or truncate text
- Correct grammar or punctuation
- Remove or add spaces
- Change capitalization
- Interpret or paraphrase content
- Omit any visible characters

Extract exactly what is displayed, character-for-character.

### **3. Webpage Component Analysis**
Identify and classify all visible components within the webpage using these categories:

**Primary Components:**
- `navigation`: Top/side navigation menus
- `header`: Site branding, logos, global actions
- `main_content`: Primary workspace area
- `sidebar`: Secondary panels, filters, drawers  
- `footer`: Bottom section content
- `overlay`: Modals, popups, notifications

**For each component, extract UI elements with these attributes:**
- `type`: Element category (button, input, link, text, image, table, dropdown, etc.)
- `label`: Visible text or accessible identifier - EXACT text without modifications
- `id`: Generated unique identifier (use descriptive naming like "btn_submit_form")
- `bounds`: Approximate position as `{x, y, width, height}` relative to viewport
- `state`: Visual state if detectable (active, disabled, selected, error, loading)
- `value`: For form inputs, any visible content - EXACT text without modifications
- `children`: For containers, nested elements

**TEXT EXTRACTION RULE:** All text values (labels, values, content) must be extracted exactly as displayed - character-perfect with no alterations, corrections, or interpretations.

### **4. Special Cases**

**Tables:**
```yaml
type: table
headers: [array of column names]
rows:
  - id: "row_1"
    cells:
      - type: text
        label: "Cell content"
        id: "cell_1_1"
      - type: actions  # For cells with buttons/icons
        children:
          - type: button
            label: "Edit"
            id: "btn_edit_row1"
```

**Forms:**
Group related form elements under a form container with validation states where visible.

**Interactive States:**
Only include state information that is visually apparent in the static screenshot.

## **Output Format**

Return ONLY valid YAML with this structure:

```yaml
browser_component:
  tab_title: "Page Title"
  url: "https://example.com/page"
  address_bar_focused: false

webpage:
  navigation:
    - type: menu
      id: "main_nav"
      bounds: {x: 0, y: 0, width: 1200, height: 60}
      children:
        - type: link
          label: "Home"
          id: "nav_home"
          state: active
  
  main_content:
    - type: form
      id: "login_form"
      bounds: {x: 400, y: 200, width: 400, height: 300}
      children:
        - type: input
          label: "Email"
          id: "input_email"
          bounds: {x: 420, y: 220, width: 360, height: 40}
          value: "<EMAIL>"
```

## **Error Handling**

If the image cannot be analyzed, return detailed error information:

```yaml
error:
  type: "ANALYSIS_FAILED"
  reason: "specific_failure_reason"  # Choose from: IMAGE_CORRUPTED, IMAGE_BLURRY, NOT_WEB_APPLICATION, INSUFFICIENT_RESOLUTION, BROWSER_CHROME_NOT_VISIBLE, CONTENT_OBSCURED, LOADING_STATE, OTHER
  message: "Detailed explanation of why analysis failed"
  details:
    image_quality: "assessment of image clarity/resolution"
    visible_elements: "what could be detected, if any"
    missing_components: "what essential elements are not visible"
```

**Error Reason Categories:**
- `IMAGE_CORRUPTED`: File appears damaged or unreadable
- `IMAGE_BLURRY`: Too blurry to read text accurately
- `NOT_WEB_APPLICATION`: Not a browser/web app screenshot
- `INSUFFICIENT_RESOLUTION`: Image too small to extract details
- `BROWSER_CHROME_NOT_VISIBLE`: Cannot locate browser tabs/address bar
- `CONTENT_OBSCURED`: Key areas covered by overlays/popups
- `LOADING_STATE`: Page appears to be loading/incomplete
- `OTHER`: Specify custom reason in message

## **Important Constraints**

1. **ASCII Only**: Use only standard ASCII characters in YAML structure (keys, formatting)
2. **EXACT TEXT EXTRACTION**: All visible text must be copied exactly - no changes, corrections, or modifications
3. **No Assumptions**: If information isn't clearly visible, use `null`
4. **Approximate Positioning**: Bounds should be reasonable estimates, not precise measurements
5. **Static Analysis**: Only describe what's visible in the static screenshot
6. **Hierarchical Structure**: Maintain logical parent-child relationships for UI components
7. **CHARACTER-PERFECT ACCURACY**: Every letter, number, space, and punctuation mark in text content must match exactly what appears on screen

## **Quality Guidelines**

- Prioritize functional elements over decorative ones
- Use consistent, descriptive naming for IDs
- Group related elements logically
- Include all interactive elements (buttons, links, inputs)
- Capture accessibility-relevant information (labels, roles)

Analyze the provided screenshot and return the YAML structure following these specifications.

```
"""

SYSTEM_PROMPT = """You are an expert in analyzing browser screenshots. 

Always follow the user instructions exactly. 

Rules:
- Treat each input as a browser screenshot (frame).  
- Extract information into two sections only: `browser_component` and `webpage`.  
- Output must be valid YAML only — no explanations, no extra text.  
- If data is missing or not visible, set the value to `null`.  
- Capture all visible details in the screenshot UI.  
- Represent the UI in a hierarchical tree structure using `component_type` and `subcomponents`. """


# --- Code ---
HISTORY_FILE = "chat_history_gemini.json"
METADATA_FILE = "metadata_gemini.json"

def configure_genai():
    """Configures the Generative AI client with the API key."""
    try:
        genai.configure(api_key=GOOGLE_API_KEY)
    except Exception as e:
        print(f"Error configuring GenAI: {e}")
        print("Please ensure you have set a valid GOOGLE_API_KEY.")
        exit()
        
def setup_output_folder():
    """Creates a new timestamped directory for the output YAML files."""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir_name = f"today_output_{timestamp}"
    output_path = pathlib.Path(output_dir_name)
    output_path.mkdir(parents=True, exist_ok=True)
    print(f"Created output directory: {output_path}")
    return output_path

async def save_history(chat_history):
    """Saves the chat history to a JSON file asynchronously."""
    serializable_history = [
        {"role": message.role, "parts": [part.text for part in message.parts]}
        for message in chat_history
    ]
    async with aiofiles.open(HISTORY_FILE, 'w') as f:
        await f.write(json.dumps(serializable_history, indent=2))
    print("Chat history saved.")

async def load_history():
    """Loads the last 20 chat history objects with non-empty parts from a JSON file asynchronously."""
    if os.path.exists(HISTORY_FILE):
        async with aiofiles.open(HISTORY_FILE, 'r') as f:
            content = await f.read()
            all_history = json.loads(content)
        
        # Filter objects where parts is not empty and contains non-empty strings
        filtered_history = [
            item for item in all_history 
            if item.get("parts") and 
            any(part.strip() for part in item["parts"] if isinstance(part, str))
        ]
        
        # Get the last 20 objects
        recent_history = filtered_history[-20:] if len(filtered_history) > 20 else filtered_history
        
        print(f"Chat history loaded: {len(recent_history)} objects from last 20 with non-empty parts.")
        return recent_history
    return []

async def load_metadata():
    """Loads the metadata from a JSON file asynchronously."""
    if os.path.exists(METADATA_FILE):
        async with aiofiles.open(METADATA_FILE, 'r') as f:
            content = await f.read()
            return json.loads(content)
    return {}

async def save_metadata(metadata):
    """Saves the metadata to a JSON file asynchronously."""
    async with aiofiles.open(METADATA_FILE, 'w') as f:
        await f.write(json.dumps(metadata, indent=2))

def generate_yaml_from_image(chat_session, image_path, prompt_text, max_retries=4):
    """
    Analyzes a single image using the Gemini Pro Vision model with a retry mechanism.
    Raises an exception if all retries fail.
    """
    print(f"-> Processing image: {image_path.name}")
    retries = 0
    while retries < max_retries:
        try:
            # Open the image
            img = Image.open(image_path)
            
            # Send the prompt and image as a single message to the existing chat session
            response = chat_session.send_message([prompt_text, img])
            print("response.text ### ",response.text)
            cleaned_response = response.text.strip()
            if cleaned_response.startswith("```yaml"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]
            return cleaned_response.strip()
            
        except FileNotFoundError:
            raise FileNotFoundError(f"Error: Image file not found at {image_path}")
        except Exception as e:
            retries += 1
            print(f"Attempt {retries}/{max_retries} failed for {image_path.name}. Error: {e}")
            if retries < max_retries:
                wait_time = 2 ** retries
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)  # Use time.sleep for synchronous wait
            else:
                # Raise an exception to stop the process
                raise Exception(f"Failed to process {image_path.name} after {max_retries} attempts.")

async def process_single_image(chat_session, image_path, prompt, output_folder, metadata, semaphore):
    """Process a single image with concurrency control."""
    async with semaphore:
        image_name = image_path.name
        
        if image_name in metadata:
            print(f"Skipping {image_name}, already processed according to metadata.json.")
            return metadata
        
        try:
            yaml_output = generate_yaml_from_image(chat_session, image_path, prompt)
            
            # Get the numeric part of the filename
            numeric_part = image_path.stem.split('_')[-1]
            output_filename = f"ui_elements_{numeric_part}.yaml"
            output_file_path = output_folder / output_filename
            
            # Write YAML output asynchronously
            async with aiofiles.open(output_file_path, 'w') as f:
                await f.write(yaml_output)

            # Update metadata
            metadata[image_name] = output_filename
            await save_metadata(metadata)

            print("-" * 50)
            print(f"File: {image_name}")
            print(f"Generated YAML written to: {output_file_path}")
            print("Generated YAML:")
            print(yaml_output)
            print("-" * 50 + "\n")
            
            await save_history(chat_session.history)
            
            return metadata
            
        except Exception as e:
            # If an exception is raised, print the error and re-raise
            print("-" * 50)
            print(f"An unrecoverable error occurred processing {image_name}: {e}")
            print("-" * 50)
            raise

async def process_all_images_in_folder(folder_path, prompt, output_folder, system_prompt, max_concurrent=1):
    """
    Iterates through all images in a folder, generates YAML asynchronously.
    max_concurrent: Maximum number of images to process simultaneously.
    """
    image_dir = pathlib.Path(folder_path)
    if not image_dir.is_dir():
        print(f"Error: The specified folder '{folder_path}' does not exist.")
        return

    image_extensions = ['.jpg']
    image_files = sorted([
        file for file in image_dir.iterdir() 
        if file.suffix.lower() in image_extensions
    ])

    if not image_files:
        print(f"No images found in the folder: {folder_path}")
        return

    print(f"Found {len(image_files)} image(s) to process.\n")
    
    metadata = await load_metadata()
    history = await load_history()
    config = GenerationConfig(temperature=0.1, top_p=0.2)
    model = genai.GenerativeModel(
        'gemini-2.5-pro',
        system_instruction=system_prompt,
        generation_config=config,
        safety_settings={
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
        }
    )
    chat = model.start_chat(history=history
    )
    
    # chat.send_message(system_prompt)
    
    if metadata:
        print("Existing metadata found. Resuming from last state.")
    else:
        print("No existing metadata found. Starting a new session.")

    # Create semaphore to limit concurrent processing
    semaphore = asyncio.Semaphore(max_concurrent)
    
    # Create tasks for all images
    tasks = []
    for image_path in image_files:
        task = process_single_image(chat, image_path, prompt, output_folder, metadata, semaphore)
        tasks.append(task)
    
    try:
        # Process all images concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check for exceptions in results
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Error processing {image_files[i].name}: {result}")
                print("Processing stopped due to error.")
                return
                
        print("All images processed successfully!")
        
    except Exception as e:
        print(f"An unrecoverable error occurred: {e}")
        print("Processing stopped.")
        return

async def main():
    """Main async function."""
    configure_genai()
    # output_dir = setup_output_folder()
    output_dir = r'E:\loveable_AI\bunch\Normal_Yamls_4_gemini'
    
    # Ensure output_dir is a Path object
    if isinstance(output_dir, str):
        output_dir = pathlib.Path(output_dir)
    
    await process_all_images_in_folder(IMAGE_FOLDER_PATH, PROMPT, output_dir, SYSTEM_PROMPT)
    print("Processing complete.")

if __name__ == "__main__":
    asyncio.run(main())