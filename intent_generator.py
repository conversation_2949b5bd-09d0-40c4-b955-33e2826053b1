import json
import google.generativeai as genai
import os
import asyncio
import re
from playwright_<PERSON>.agent import PlaywrightAgent

class IntentGenerator:
    def __init__(self, api_key='AIzaSyDFznf3ccZgiJTk2avzv1BHZq2joX0VeJU'):
        """Initialize the Intent Generator with API key"""
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.5-flash')
        self.agent = PlaywrightAgent()

    def parameterize_input_values(self, result_json):
        """
        Parameterize input values in the result JSON for local saving
        """
        if not isinstance(result_json, dict):
            return result_json
            
        # Create a deep copy to avoid modifying the original
        parameterized_result = json.loads(json.dumps(result_json))
        
        # Common patterns for input values that should be parameterized
        input_patterns = [
            r'password["\s]*[:\s]*["\s]*([^"]+)["\s]*',  # password fields
            r'username["\s]*[:\s]*["\s]*([^"]+)["\s]*',  # username fields
            r'email["\s]*[:\s]*["\s]*([^"@]+@[^"]+)["\s]*',  # email fields
            r'phone["\s]*[:\s]*["\s]*([0-9\-\+\(\)\s]+)["\s]*',  # phone numbers
            r'search["\s]*[:\s]*["\s]*([^"]+)["\s]*',  # search terms
            r'name["\s]*[:\s]*["\s]*([^"]+)["\s]*',  # name fields
        ]
        
        def parameterize_text(text):
            if not isinstance(text, str):
                return text
                
            # Replace common input patterns with parameters
            parameterized = text
            
            # Password parameterization
            parameterized = re.sub(r'(password["\s]*[:\s]*["\s]*)([^"]+)(["\s]*)', 
                                 r'\1{{password}}\3', parameterized, flags=re.IGNORECASE)
            
            # Username parameterization
            parameterized = re.sub(r'(username["\s]*[:\s]*["\s]*)([^"]+)(["\s]*)', 
                                 r'\1{{username}}\3', parameterized, flags=re.IGNORECASE)
            
            # Email parameterization
            parameterized = re.sub(r'([^@\s]+@[^@\s]+\.[^@\s]+)', 
                                 r'{{email}}', parameterized)
            
            # Phone number parameterization
            parameterized = re.sub(r'(\+?[\d\s\-\(\)]{10,})', 
                                 r'{{phone}}', parameterized)
            
            # Generic input field parameterization for common phrases
            input_keywords = ['enter', 'input', 'type', 'fill', 'provide']
            for keyword in input_keywords:
                # Look for patterns like "enter 'value'" or "type 'value'"
                pattern = rf'({keyword}\s+["\']?)([^"\']+)(["\']?)'
                if re.search(pattern, parameterized, re.IGNORECASE):
                    # Only parameterize if it looks like user input (not UI elements)
                    matches = re.finditer(pattern, parameterized, re.IGNORECASE)
                    for match in matches:
                        value = match.group(2).strip()
                        # Skip if it's likely a UI element name
                        if not any(ui_term in value.lower() for ui_term in ['button', 'field', 'box', 'menu', 'tab']):
                            parameterized = parameterized.replace(match.group(0), 
                                                                f"{match.group(1)}{{{{input_value}}}}{match.group(3)}")
            
            return parameterized
        
        # Recursively parameterize all string values in the JSON
        def recursive_parameterize(obj):
            if isinstance(obj, dict):
                return {k: recursive_parameterize(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [recursive_parameterize(item) for item in obj]
            elif isinstance(obj, str):
                return parameterize_text(obj)
            else:
                return obj
        
        return recursive_parameterize(parameterized_result)

    async def analyze_video_frames(self, json_file_path, isIntent=None):
        """
        Analyze video frame data using Google's Generative AI
        """
        # Load the JSON data (small file I/O; keep sync for simplicity)
        with open(json_file_path, 'r', encoding='utf-8') as file:
            frame_data = json.load(file)

        # Create the prompt with the frame data
        prompt = f"""
        You are an expert AI assistant specializing in process discovery and documentation from video feeds. Your primary function is to analyze user interaction recordings, which are composed of video frame data, to understand and document the actions performed by a user in a software application.

        Based on this video frame data, analyze the user's actions and generate a structured JSON output.

        Video Frame Data:
        {json.dumps(frame_data, indent=2)}

        **Core Tasks:**

        1. **Identify User Actions**: Analyze the sequence of UI interactions. Pay attention to:
        - Mouse movements and position changes
        - Navigation events (URL changes)
        - UI element interactions (clicks, typing, selections)
        - Tab switches and page transitions
        - Input field interactions (text input, dropdown selections, checkbox/radio button selections)

        2. **Capture Input Values**: When users enter data into input fields, dropdowns, or make selections, capture the exact values they provided.

        3. **Infer Intent**: Determine the user's overall goal based on the sequence of actions.

        4. **Generate Step-by-Step Guide**: Produce clear, human-readable steps that include specific values entered by the user.

        5. **Normalize Page URLs**: When generating the `page_url` field in the output, ensure all URLs are complete and include the correct protocol (`http` or `https`). If any `address_bar` values in the video frame data are missing the protocol (e.g., just a domain or path), infer the correct protocol dynamically by checking other `address_bar` entries with the same host that do include the protocol. Use the protocol from any matching full address to normalize all related entries.

        **Output Format:**

        Return ONLY a valid JSON object with this exact structure:

        {{
        "intent": "A high-level description of the user's overall goal",
        "action_summary": "A single, concise sentence summarizing the entire sequence of actions",
        "steps": [
            {{
            "step_number": 1,
            "action": "Clear description of the user's action including specific values entered",
            "details": {{
                "target_element": "UI element interacted with",
                "input_value": "Actual value entered by user (if applicable)",
                "cursor_position": [x, y],
                "page_url": "URL when action occurred (must include correct protocol)"
            }}
            }}
        ]
        }}
        """

        try:
            # Generate the response without blocking the event loop
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, self.model.generate_content, prompt)

            print("intent : ", response.text)

            if isIntent == "active":
            # Return just the intent analysis without invoking the agent
                try:
                    return json.loads(response.text)
                except json.JSONDecodeError:
                    return {"status": False, "error_stage": "intent_parsing", "error_message": "Failed to parse intent response as JSON"}

            # Send original response to agent (already async)
            finalresp = await self.agent.invoke_user_input(response.text)

            # If the agent returned a structured error, propagate it
            if isinstance(finalresp, dict) and not finalresp.get("status", True):
                return {"status": False, "error_stage": "playwright_agent", "error_message": finalresp.get("data")}

            # Ensure finalresp is JSON if it's a string
            while isinstance(finalresp, str):
                try:
                    finalresp = json.loads(finalresp)
                    break
                except json.JSONDecodeError:
                    break

            return finalresp

        except json.JSONDecodeError as e:
            print(f"Error parsing JSON response: {e}")
            print("Raw response:", response.text if 'response' in locals() else '')
            return {"status": False, "error_stage": "intent_generator", "error_message": str(e)}
        except Exception as e:
            print(f"Error during analysis: {e}")
            return {"status": False, "error_stage": "intent_generator", "error_message": str(e)}

    def analyze_video_frames_sync(self, json_file_path, isIntent=None):
        """
        Synchronous wrapper for analyze_video_frames
        """
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        try:
            return loop.run_until_complete(self.analyze_video_frames(json_file_path,isIntent))
        finally:
            # Don't close the loop if it was already running
            if not loop.is_running():
                loop.close()

    def process_json_file(self, json_file_path):
        """
        Process a JSON file and return the analysis result
        """
        # Check if file exists
        if not os.path.exists(json_file_path):
            print(f"Error: JSON file does not exist: {json_file_path}")
            return None

        # Analyze the video frames
        result = self.analyze_video_frames_sync(json_file_path, isIntent=None)

        if result:
            print("\nAnalysis completed successfully!")
            
            # Generate output filename based on input filename
            input_filename = os.path.splitext(os.path.basename(json_file_path))[0]
            output_file = os.path.join(os.path.dirname(json_file_path) or '.', f"intent_analysis_{input_filename}.json")
            
            try:
                # Create parameterized version for local saving
                parameterized_result = self.parameterize_input_values(result)
                
                # Save parameterized version to local file
                os.makedirs(os.path.dirname(output_file) or '.', exist_ok=True)
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(parameterized_result, f, indent=2, ensure_ascii=False)
                print(f"Parameterized result saved to: {output_file}")
                
                # Return original result (not parameterized)
                return result
                
            except Exception as e:
                print(f"Error saving file: {e}")
                return result
        else:
            print("Analysis failed!")
            return None

# Backward compatibility for standalone usage
async def main():
    import sys
    
    # Check if JSON file path is provided as command line argument
    if len(sys.argv) > 1:
        json_file_path = sys.argv[1]
        print(f"Using provided JSON file: {json_file_path}")
    else:
        # Fallback to hardcoded path if no argument provided
        json_file_path = r"E:\loveable_AI\bunch\yaml_analysis_results_20250819_172925.json"
        print(f"No file path provided, using default: {json_file_path}")

    intent_gen = IntentGenerator()
    result = await intent_gen.analyze_video_frames(json_file_path, isIntent=None)
    
    if result:
        print("\nAnalysis completed successfully!")
        
        # Generate output filename based on input filename
        input_filename = os.path.splitext(os.path.basename(json_file_path))[0]
        output_file = os.path.join(os.path.dirname(json_file_path) or '.', f"intent_analysis_{input_filename}.json")
        
        try:
            # Create parameterized version for local saving
            intent_gen_instance = IntentGenerator()
            parameterized_result = intent_gen_instance.parameterize_input_values(result)
            
            # Save parameterized version
            os.makedirs(os.path.dirname(output_file) or '.', exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(parameterized_result, f, indent=2, ensure_ascii=False)
            print(f"Parameterized result saved to: {output_file}")
            
        except Exception as e:
            print(f"Error saving file: {e}")
    else:
        print("Analysis failed!")

if __name__ == "__main__":
    asyncio.run(main())