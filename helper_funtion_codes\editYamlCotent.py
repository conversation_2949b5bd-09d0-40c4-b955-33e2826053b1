import json
import os
from pathlib import Path

def remove_field_from_json(input_file_path, field_path, output_file_path=None):
    """
    Remove a specific field from JSON file and save the result
    
    Args:
        input_file_path (str): Path to the input JSON file
        field_path (str): Dot-separated path to the field to remove (e.g., 'file_details.yaml_content')
        output_file_path (str, optional): Path for output file. If None, creates a new file with '_modified' suffix
    """
    
    try:
        # Read the JSON file
        with open(input_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Parse the field path
        field_parts = field_path.split('.')
        
        # Function to recursively remove field
        def remove_field_recursive(obj, parts):
            if len(parts) == 1:
                # Last part - remove the field if it exists
                if isinstance(obj, dict) and parts[0] in obj:
                    del obj[parts[0]]
                elif isinstance(obj, list):
                    for item in obj:
                        if isinstance(item, dict) and parts[0] in item:
                            del item[parts[0]]
            else:
                # Navigate deeper into the structure
                current_field = parts[0]
                remaining_parts = parts[1:]
                
                if isinstance(obj, dict) and current_field in obj:
                    remove_field_recursive(obj[current_field], remaining_parts)
                elif isinstance(obj, list):
                    for item in obj:
                        if isinstance(item, dict) and current_field in item:
                            remove_field_recursive(item[current_field], remaining_parts)
        
        # Remove the field from data
        remove_field_recursive(data, field_parts)
        
        # Determine output file path
        if output_file_path is None:
            input_path = Path(input_file_path)
            output_file_path = input_path.parent / f"{input_path.stem}_modified{input_path.suffix}"
        
        # Save the modified JSON
        with open(output_file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2, ensure_ascii=False)
        
        print(f"✅ Successfully removed field '{field_path}' from JSON")
        print(f"📁 Modified JSON saved to: {output_file_path}")
        
        return str(output_file_path)
        
    except FileNotFoundError:
        print(f"❌ Error: File '{input_file_path}' not found")
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"❌ Error: {e}")
        
    return None

def remove_yaml_content_specifically(input_file_path, output_file_path=None):
    """
    Specifically remove yaml_content field from the given JSON structure
    """
    try:
        # Read the JSON file
        with open(input_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Remove yaml_content from each object in the array
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and 'file_details' in item:
                    if isinstance(item['file_details'], dict) and 'yaml_content' in item['file_details']:
                        del item['file_details']['yaml_content']
        
        # Determine output file path
        if output_file_path is None:
            input_path = Path(input_file_path)
            output_file_path = input_path.parent / f"{input_path.stem}_no_yaml{input_path.suffix}"
        
        # Save the modified JSON
        with open(output_file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2, ensure_ascii=False)
        
        print(f"✅ Successfully removed 'yaml_content' fields from JSON")
        print(f"📁 Modified JSON saved to: {output_file_path}")
        
        return str(output_file_path)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

# Example usage
if __name__ == "__main__":
    # Example 1: Using the general function
    input_file = r"E:\loveable_AI\bunch\yaml_analysis_results.json"  # Replace with your actual file path
    
    # Remove yaml_content field specifically
    remove_field_from_json(input_file, "file_details.yaml_content")
    
    # Example 2: Using the specific function for your use case
    # remove_yaml_content_specifically(input_file)
    
    # Example 3: Specify custom output path
    #