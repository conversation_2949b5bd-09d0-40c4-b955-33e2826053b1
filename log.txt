- image_name: frame_0000.jpg
  success: "Able to generate yaml"
  timestamp: "2025-08-23T20:26:34.635258"

- image_name: frame_0001.jpg
  error: "'NoneType' object has no attribute 'strip'"
  timestamp: "2025-08-23T20:26:41.605980"

- image_name: frame_0001.jpg
  error: |-
    litellm.InternalServerError: VertexAIException InternalServerError - {
      "error": {
        "code": 500,
        "message": "An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting",
        "status": "INTERNAL"
      }
    }
  timestamp: "2025-08-23T20:26:49.864488"

- image_name: frame_0001.jpg
  error: "'NoneType' object has no attribute 'strip'"
  timestamp: "2025-08-23T20:27:03.531180"

- image_name: frame_0001.jpg
  error: "'NoneType' object has no attribute 'strip'"
  timestamp: "2025-08-23T20:28:12.374947"

- image_name: frame_0001.jpg
  error: |-
    litellm.InternalServerError: VertexAIException InternalServerError - {
      "error": {
        "code": 500,
        "message": "An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting",
        "status": "INTERNAL"
      }
    }
  timestamp: "2025-08-23T20:28:20.623206"

- image_name: frame_0001.jpg
  error: "'NoneType' object has no attribute 'strip'"
  timestamp: "2025-08-23T20:28:33.918894"

- image_name: frame_0001.jpg
  error: |-
    litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
      "error": {
        "code": 429,
        "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
        "status": "RESOURCE_EXHAUSTED",
        "details": [
          {
            "@type": "type.googleapis.com/google.rpc.QuotaFailure",
            "violations": [
              {
                "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
                "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
                "quotaDimensions": {
                  "location": "global",
                  "model": "gemini-2.5-pro"
                },
                "quotaValue": "50"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.Help",
            "links": [
              {
                "description": "Learn more about Gemini API quotas",
                "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.RetryInfo",
            "retryDelay": "4s"
          }
        ]
      }
    }
  timestamp: "2025-08-23T20:28:56.131703"

- image_name: frame_0001.jpg
  error: |-
    litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
      "error": {
        "code": 429,
        "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
        "status": "RESOURCE_EXHAUSTED",
        "details": [
          {
            "@type": "type.googleapis.com/google.rpc.QuotaFailure",
            "violations": [
              {
                "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
                "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
                "quotaDimensions": {
                  "location": "global",
                  "model": "gemini-2.5-pro"
                },
                "quotaValue": "2"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.Help",
            "links": [
              {
                "description": "Learn more about Gemini API quotas",
                "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.RetryInfo",
            "retryDelay": "57s"
          }
        ]
      }
    }
  timestamp: "2025-08-23T20:29:02.515260"

- image_name: frame_0001.jpg
  error: |-
    litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
      "error": {
        "code": 429,
        "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
        "status": "RESOURCE_EXHAUSTED",
        "details": [
          {
            "@type": "type.googleapis.com/google.rpc.QuotaFailure",
            "violations": [
              {
                "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
                "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
                "quotaDimensions": {
                  "location": "global",
                  "model": "gemini-2.5-pro"
                },
                "quotaValue": "50"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.Help",
            "links": [
              {
                "description": "Learn more about Gemini API quotas",
                "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.RetryInfo",
            "retryDelay": "46s"
          }
        ]
      }
    }
  timestamp: "2025-08-23T20:29:14.040443"

- image_name: frame_0001.jpg
  error: "'NoneType' object has no attribute 'strip'"
  timestamp: "2025-08-23T20:30:07.017295"

- image_name: frame_0001.jpg
  error: |-
    litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
      "error": {
        "code": 429,
        "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
        "status": "RESOURCE_EXHAUSTED",
        "details": [
          {
            "@type": "type.googleapis.com/google.rpc.QuotaFailure",
            "violations": [
              {
                "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
                "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
                "quotaDimensions": {
                  "model": "gemini-2.5-pro",
                  "location": "global"
                },
                "quotaValue": "50"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.Help",
            "links": [
              {
                "description": "Learn more about Gemini API quotas",
                "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.RetryInfo",
            "retryDelay": "46s"
          }
        ]
      }
    }
  timestamp: "2025-08-23T20:30:13.590012"

- image_name: frame_0001.jpg
  error: |-
    litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
      "error": {
        "code": 429,
        "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
        "status": "RESOURCE_EXHAUSTED",
        "details": [
          {
            "@type": "type.googleapis.com/google.rpc.QuotaFailure",
            "violations": [
              {
                "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
                "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
                "quotaDimensions": {
                  "model": "gemini-2.5-pro",
                  "location": "global"
                },
                "quotaValue": "50"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.Help",
            "links": [
              {
                "description": "Learn more about Gemini API quotas",
                "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
              }
            ]
          },
          {
            "@type": "type.googleapis.com/google.rpc.RetryInfo",
            "retryDelay": "35s"
          }
        ]
      }
    }
  timestamp: "2025-08-23T20:30:24.969309"
