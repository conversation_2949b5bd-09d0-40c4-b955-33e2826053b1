import os
import pathlib
from litellm import acompletion,batch_completion,completion
import asyncio
import aiofiles
import time
from datetime import datetime
import json
import base64
# import winsound
import sys
import yaml  # <-- 1. Import the PyYAML library
from yaml import YAMLError #
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# --- Configuration ---
# Array of API keys for round-robin rotation
API_KEYS = [
 os.getenv('GOOGLE_API_KEY1'),
 os.getenv('GOOGLE_API_KEY2'),
 os.getenv('GOOGLE_API_KEY3'),
 os.getenv('GOOGLE_API_KEY4'),
 os.getenv('GOOGLE_API_KEY5'),
 os.getenv('GOOGLE_API_KEY6'),
 os.getenv('GOOGLE_API_KEY7'),
 os.getenv('GOOGLE_API_KEY8'),
 os.getenv('GOOGLE_API_KEY9'),
 os.getenv('GOOGLE_API_KEY10'),
 os.getenv('GOOGLE_API_KEY11'),
 os.getenv('GOOGLE_API_KEY12'),
 os.getenv('GOOGLE_API_KEY13'),
 os.getenv('GOOGLE_API_KEY14'),
 os.getenv('GOOGLE_API_KEY15'),
 os.getenv('GOOGLE_API_KEY16'),
 os.getenv('GOOGLE_API_KEY17'),
 os.getenv('GOOGLE_API_KEY18'),
 os.getenv('GOOGLE_API_KEY19')
]

# Global variables for API key rotation
current_api_key_index = 0
GEMINI_API_KEY = API_KEYS[current_api_key_index]

rotated_key = False

# IMAGE_FOLDER_PATH = r'E:\loveable_AI\bunch\finalFrames_20250822_090407'

IMAGE_FOLDER_PATH = pathlib.Path("./input")    # ./input folder inside your project
OUTPUT_FOLDER_PATH = pathlib.Path("./output")

OUTPUT_FOLDER_PATH.mkdir(parents=True, exist_ok=True)


PROMPT = """


# UI Screenshot Analysis Prompt (Improved Version)

You are an expert UI analyst. Your task is to analyze a web application screenshot and generate a structured YAML representation of its components.

## **Core Requirements**

### **1. Primary Classification**
Classify the screenshot into two main regions:
- `browser_component`: Browser chrome (tabs, address bar, navigation buttons)
- `webpage`: Main content viewport

### **2. Browser Component Extraction**
From the browser chrome, extract only:
- `tab_title`: Active tab title text - EXACT text without any modifications, abbreviations, or corrections
- `url`: Complete URL visible in address bar - EXACT characters including all parameters, fragments, and special characters
- `address_bar_focused`: Boolean indicating if address bar is active/selected

**CRITICAL ACCURACY REQUIREMENT:**
All text extraction must be CHARACTER-PERFECT. Do not:
- Fix spelling errors
- Abbreviate or truncate text
- Correct grammar or punctuation
- Remove or add spaces
- Change capitalization
- Interpret or paraphrase content
- Omit any visible characters

Extract exactly what is displayed, character-for-character.

### **3. Webpage Component Analysis**
Identify and classify all visible components within the webpage using these categories:

**Primary Components:**
- `navigation`: Top/side navigation menus
- `header`: Site branding, logos, global actions
- `main_content`: Primary workspace area
- `sidebar`: Secondary panels, filters, drawers  
- `footer`: Bottom section content
- `overlay`: Modals, popups, notifications

**For each component, extract UI elements with these attributes:**
- `type`: Element category (button, input, link, text, image, table, dropdown, etc.)
- `label`: Visible text or accessible identifier - EXACT text without modifications
- `id`: Generated unique identifier (use descriptive naming like "btn_submit_form")
- `bounds`: Approximate position as `{x, y, width, height}` relative to viewport
- `state`: Visual state if detectable (active, disabled, selected, error, loading, **hovered**, **clicked**)
- `value`: For form inputs, any visible content - EXACT text without modifications
- `children`: For containers, nested elements

**TEXT EXTRACTION RULE:** All text values (labels, values, content) must be extracted exactly as displayed - character-perfect with no alterations, corrections, or interpretations.

### **4. Special Cases**

**Tables:**
```yaml
type: table
headers: [array of column names]
rows:
  - id: "row_1"
    cells:
      - type: text
        label: "Cell content"
        id: "cell_1_1"
      - type: actions  # For cells with buttons/icons
        children:
          - type: button
            label: "Edit"
            id: "btn_edit_row1"
````

**Forms:**
Group related form elements under a form container with validation states where visible.

**Interactive States:**
Only include state information that is visually apparent in the static screenshot.

  - **Hover/Click States**: For buttons or links, identify `hovered` or `clicked` states based on visual cues like a change in background color, different border, shadow effect, underline, or a depressed/inset appearance.

## **Output Format**

Return ONLY valid YAML with this structure:

```yaml
browser_component:
  tab_title: "Page Title"
  url: "https://example.com/page"
  address_bar_focused: false

webpage:
  navigation:
    - type: menu
      id: "main_nav"
      bounds: {x: 0, y: 0, width: 1200, height: 60}
      children:
        - type: link
          label: "Home"
          id: "nav_home"
          state: active
  
  main_content:
    - type: form
      id: "login_form"
      bounds: {x: 400, y: 200, width: 400, height: 300}
      children:
        - type: input
          label: "Email"
          id: "input_email"
          bounds: {x: 420, y: 220, width: 360, height: 40}
          value: "<EMAIL>"
        - type: button
          label: "Login"
          id: "btn_login"
          bounds: {x: 420, y: 280, width: 360, height: 40}
          state: hovered
```

## **Error Handling**

If the image cannot be analyzed, return detailed error information:

```yaml
error:
  type: "ANALYSIS_FAILED"
  reason: "specific_failure_reason"  # Choose from: IMAGE_CORRUPTED, IMAGE_BLURRY, NOT_WEB_APPLICATION, INSUFFICIENT_RESOLUTION, BROWSER_CHROME_NOT_VISIBLE, CONTENT_OBSCURED, LOADING_STATE, OTHER
  message: "Detailed explanation of why analysis failed"
  details:
    image_quality: "assessment of image clarity/resolution"
    visible_elements: "what could be detected, if any"
    missing_components: "what essential elements are not visible"
```

**Error Reason Categories:**

  - `IMAGE_CORRUPTED`: File appears damaged or unreadable
  - `IMAGE_BLURRY`: Too blurry to read text accurately
  - `NOT_WEB_APPLICATION`: Not a browser/web app screenshot
  - `INSUFFICIENT_RESOLUTION`: Image too small to extract details
  - `BROWSER_CHROME_NOT_VISIBLE`: Cannot locate browser tabs/address bar
  - `CONTENT_OBSCURED`: Key areas covered by overlays/popups
  - `LOADING_STATE`: Page appears to be loading/incomplete
  - `OTHER`: Specify custom reason in message

## **Important Constraints**

1.  **ASCII Only**: Use only standard ASCII characters in YAML structure (keys, formatting)
2.  **EXACT TEXT EXTRACTION**: All visible text must be copied exactly - no changes, corrections, or modifications
3.  **No Assumptions**: If information isn't clearly visible, use `null`
4.  **Approximate Positioning**: Bounds should be reasonable estimates, not precise measurements
5.  **Static Analysis**: Only describe what's visible in the static screenshot
6.  **Hierarchical Structure**: Maintain logical parent-child relationships for UI components
7.  **CHARACTER-PERFECT ACCURACY**: Every letter, number, space, and punctuation mark in text content must match exactly what appears on screen

## **Quality Guidelines**

  - Prioritize functional elements over decorative ones
  - Use consistent, descriptive naming for IDs
  - Group related elements logically
  - Include all interactive elements (buttons, links, inputs)
  - Capture accessibility-relevant information (labels, roles)

Analyze the provided screenshot and return the correct valid YAML structure following these specifications without any Incomplete or invalid syntax issue.


"""

SYSTEM_PROMPT = """You are an expert in analyzing browser screenshots. 

Always follow the user instructions exactly. 

Rules:
- Treat each input as a browser screenshot (frame).  
- Extract information into two sections only: `browser_component` and `webpage`.  
- Output must be valid YAML only – no explanations, no extra text.  
- If data is missing or not visible, set the value to `null`.  
- Capture all visible details in the screenshot UI.  
- Represent the UI in a hierarchical tree structure using `component_type` and `subcomponents`. 


"""

def rotate_api_key():
    """Rotate to the next API key in round-robin fashion."""
    global current_api_key_index, GEMINI_API_KEY
    current_api_key_index = (current_api_key_index + 1) % len(API_KEYS)
    GEMINI_API_KEY = API_KEYS[current_api_key_index]
    print(f"Rotated to API key index: {current_api_key_index}")

# --- Code ---
HISTORY_FILE =  "history.json"
LOG_FILE =  "logs.json"


def encode_image(image_path):
    """Encode the image to base64."""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def setup_output_folder():
    """Creates a new timestamped directory for the output YAML files."""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir_name = f"today_output_{timestamp}"
    output_path = pathlib.Path(output_dir_name)
    output_path.mkdir(parents=True, exist_ok=True)
    print(f"Created output directory: {output_path}")
    return output_path

async def save_history(history):
    """Saves the chat history to a JSON file asynchronously."""
    serializable_history = [
        {"role": message["role"], "parts": message["parts"]}
        for message in history
    ]
    async with aiofiles.open(HISTORY_FILE, 'w', encoding='utf-8') as f:
        await f.write(json.dumps(serializable_history, indent=2))
    print("Chat history saved.")

# async def load_history():
#     """Loads the last 9 chat history entries from a JSON file asynchronously."""
#     if not os.path.exists(HISTORY_FILE):
#         return []
#     try:
#         async with aiofiles.open(HISTORY_FILE, 'r', encoding='utf-8') as f:
#             content = await f.read()
#             if not content:
#                 return []
#             all_history = json.loads(content)
        
#         # Simply get the last 9 entries, regardless of content
#         recent_history = all_history[-9:]
#         print(f"Chat history loaded: {len(recent_history)} entries.")
#         return recent_history
#     except (json.JSONDecodeError, IOError) as e:
#         print(f"Could not load or parse history file: {e}")
#         return []


async def load_history():
    """Loads the last 9 chat history objects with non-empty parts from a JSON file asynchronously."""
    if os.path.exists(HISTORY_FILE):
        async with aiofiles.open(HISTORY_FILE, 'r', encoding='utf-8') as f:
            content = await f.read()
            all_history = json.loads(content)

            # Filter objects where parts is not empty and contains non-empty strings
            filtered_history = [
                item for item in all_history
                if item.get("parts") and any(
                    part.strip() for part in item["parts"] if isinstance(part, str)
                )
            ]

            # Get the last 9 objects
            recent_history = (
                filtered_history[-9:] if len(filtered_history) > 9 else filtered_history
            )

            print(
                f"Chat history loaded: {len(recent_history)} objects "
                f"from last 9 with non-empty parts."
            )
            return recent_history

    return []


async def load_log():
    """Loads all log entries from log.json asynchronously."""
    if os.path.exists(LOG_FILE):
        async with aiofiles.open(LOG_FILE, 'r', encoding='utf-8') as f:
            content = await f.read()
            try:
                return json.loads(content)  # returns list of logs
            except json.JSONDecodeError:
                return []  # if log.json is corrupted
    return []

async def get_last_attempt(image_name: str) -> int:
    """Return the attempt number of the last log entry for the given image_name.
    If not found, return 1."""
    if not os.path.exists(LOG_FILE):
        return 0

    async with aiofiles.open(LOG_FILE, 'r', encoding='utf-8') as f:
        content = await f.read()
        try:
            logs = json.loads(content)
        except json.JSONDecodeError:
            return 0

    # Filter logs for given image_name
    matches = [entry for entry in logs if entry.get("image_name") == image_name]

    if matches:
        return matches[-1].get("attempt", 1)  # last matching entry's attempt
    return 0



async def generate_yaml_from_image(history, image_path, prompt_text,image_name):
    """
    Analyzes a single image using the Gemini Pro Vision model with a retry mechanism.
    Retries if the API call fails OR if the generated YAML is invalid.
    Raises an exception if all retries fail.
    """
    global rotated_key
    print(f"-> Processing image: {image_path.name}")
    lastTry = 0
    retries = 0
    initialWaitTime=0
    max_retries = 4
    while retries < max_retries:
        try:
            base64_image = encode_image(image_path)
            
            messages = []
            messages.append({"role": "system", "content": SYSTEM_PROMPT})
            
            # for hist in history:
            #     content = [{"type": "text", "text": part} for part in hist["parts"]]
            #     messages.append({"role": hist["role"], "content": content})


            # Correctly format the chat history
            for hist_item in history:
                # The content for the 'assistant' (model) role must be a simple string.
                if hist_item["role"] == "model" and hist_item.get("parts"):
                    message_content = hist_item["parts"][0]
                    # Use 'assistant' as the role, which is standard.
                    messages.append({"role": "assistant", "content": message_content})
                
                # Note: The current script doesn't save user messages to history,
                # but if it did, you would handle the 'user' role here.
            
            user_content = [
                {"type": "text", "text": prompt_text},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
            ]
            messages.append({"role": "user", "content": user_content})
            print("to begin sending data to LLM")
            response = await  acompletion(
                model="gemini/gemini-2.5-flash",
                timeout=900,
                messages=messages,
                temperature=0.0
            )
            
            print("^^^^RESPONE^^^ : ", response)
                    # Access and print the usage details
            print("--- Token Usage Details ---")
            if response.usage:
                print(f"Prompt Tokens: {response.usage.prompt_tokens}")
                print(f"Completion Tokens: {response.usage.completion_tokens}")
                print(f"Total Tokens: {response.usage.total_tokens} 🪙")
            else:
                print("Usage information not available in the response.")
            assistant_text = response.choices[0].message.content


            if assistant_text is None:
                raise Exception(f"Assistant response is None: {response}")
            
            cleaned_response = assistant_text.strip()
            
            if cleaned_response.startswith("```yaml"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]
            
            cleaned_response = cleaned_response.strip()

            # --- 2. Add YAML validation logic ---
            try:
                yaml.safe_load(cleaned_response)
                print(f"Successfully generated and validated YAML for {image_path.name}.")
                # If parsing succeeds, the YAML is valid, so we return it.
                # is_log_rotation = rotated_key
                token_usage = getattr(getattr(response, "usage", None), "prompt_tokens", None)
                last_attempt = await get_last_attempt(image_name)
                await log_response(image_name, "Able to generate UI elements","success",last_attempt+1,token_usage,0,rotated_key)
                rotated_key=False
                return cleaned_response, assistant_text
            except YAMLError as e:
                # If parsing fails, raise an exception to trigger the retry logic.
                raise ValueError(f"Generated content is not valid YAML. Error: {e}")
            # ------------------------------------
            
        except FileNotFoundError:
            raise FileNotFoundError(f"Error: Image file not found at {image_path}")
        except Exception as e: # This will now catch both API errors and our ValueError
            print("^^^^RESPONE^^^ Exception: ", repr(e))
            retries += 1
            last_attempt = await get_last_attempt(image_name)
            print("last_attempt >>> ",last_attempt)
            print("retries >>> ",retries)
            attempt = 1 if last_attempt == 0 else last_attempt + 1

            print(f"Attempt {retries}/{max_retries} failed for {image_path.name}. Error: {e}")
            if retries < max_retries:
                wait_time = 30 * retries
                initialWaitTime=wait_time
                token_usage = None
                if 'response' in locals() and hasattr(response, "usage"):
                    token_usage = getattr(getattr(response, "usage", None), "prompt_tokens", None)
                await log_response(image_name, str(e),"error",attempt,token_usage,initialWaitTime,rotated_key)
                rotated_key=False
                print(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
            else:
                # winsound.Beep(440, 500)   # A4 note, 0.5 sec 
                # winsound.Beep(880, 500)   # A5 note, 0.5 sec
                # winsound.Beep(660, 1000)  # E5 note, 1 sec
                # Raise an exception to stop the process
                raise Exception(f"Failed to process {image_path.name} after {max_retries} attempts.,{e}")

async def process_single_image(history, image_path, prompt, output_folder, logs, semaphore):
    """Process a single image with concurrency control."""
    async with semaphore:
        image_name = image_path.name
        
        # if image_name in metadata:
        #     print(f"Skipping {image_name}, already processed according to metadata.json.")
        #     return metadata

        if any(entry.get("image_name") == image_name and "success" in entry for entry in logs):
            print(f"Skipping {image_name}, already processed successfully according to log.json.")
            return {"image_name": image_name, "status": "skipped", "reason": "already processed"}
        
        try:
            yaml_output, assistant_text = await generate_yaml_from_image(history, image_path, prompt,image_name)
            
            # Extract frame ID and timestamp from filename
            input_name = image_path.stem
            # Pattern to match frame_XXXX_YYY (with timestamp)
            import re
            pattern_with_timestamp = r'frame_(\d{4})_(\d{3})'
            match = re.search(pattern_with_timestamp, input_name)
            if match:
                frame_id, timestamp = match.group(1), match.group(2)
                output_filename = f"ui_elements_{frame_id}_{timestamp}.yaml"
            else:
                # Fallback to old logic for backward compatibility
                numeric_part = image_path.stem.split('_')[-1]
                output_filename = f"ui_elements_{numeric_part}.yaml"
            output_file_path = output_folder / output_filename
            
            # Write YAML output asynchronously
            async with aiofiles.open(output_file_path, 'w', encoding='utf-8') as f:
                await f.write(yaml_output)
            

            print("assistant_text ### ", assistant_text)
            # Update history
            # history.append({"role": "user", "parts": [prompt]})
            history.append({"role": "model", "parts": [assistant_text]})
            
            # Update metadata
            # metadata[image_name] = output_filename
            # await save_metadata(metadata)
            
            print("-" * 50)
            print(f"File: {image_name}")
            print(f"Generated YAML written to: {output_file_path}")
            print("Generated YAML:")
            print(yaml_output)
            print("-" * 50 + "\n")
            
            await save_history(history)
            
            return {"image_name": image_name, "status": "success", "output_file": str(output_file_path)}
            
        except Exception as e:
            # If an exception is raised, print the error and re-raise to halt processing
            print("-" * 50)
            print(f"An unrecoverable error occurred processing {image_name}: {e}")
            print("-" * 50)
            raise

async def log_response(image_name, message, resultKey, attempt,tokens ,wait_time,is_rotated_key=None):
    """Logs error to the error JSON file as array of objects."""
    error_list = []
    print("call comes",image_name, message,resultKey,attempt)
    
    # Load existing errors if file exists
    if os.path.exists(LOG_FILE):
        try:
            async with aiofiles.open(LOG_FILE, 'r', encoding='utf-8') as f:
                content = await f.read()
                error_list = json.loads(content)
        except:
            error_list = []
    
    # Add new error to the array
    error_list.append({
        "image_name": image_name,
        resultKey: str(message),
        "tokens":tokens,
        "wait_time":wait_time,
        "api_key_rotation":is_rotated_key,
        "timestamp": datetime.now().isoformat(),
        "attempt": attempt
    })
    
    # Save updated errors
    async with aiofiles.open(LOG_FILE, 'w', encoding='utf-8') as f:
        await f.write(json.dumps(error_list, indent=2))

def check_if_all_images_processed():
    """Check if all images in the input folder have been processed successfully."""
    image_dir = pathlib.Path(IMAGE_FOLDER_PATH)
    image_extensions = ['.jpg']
    image_files = [
        file for file in image_dir.iterdir() 
        if file.suffix.lower() in image_extensions
    ]
    
    if not os.path.exists(LOG_FILE):
        return False
    
    try:
        with open(LOG_FILE, 'r', encoding='utf-8') as f:
            logs = json.loads(f.read())
        
        processed_images = set()
        for entry in logs:
            if entry.get("success") == "Able to generate UI elements":
                processed_images.add(entry.get("image_name"))
        
        total_images = len(image_files)
        processed_count = len(processed_images)
        
        print(f"Progress: {processed_count}/{total_images} images processed")
        return processed_count == total_images
    except:
        return False

async def process_all_images_in_folder(folder_path, prompt, output_folder, system_prompt, max_concurrent=1):
    """
    Iterates through all images in a folder, generates YAML asynchronously.
    max_concurrent: Maximum number of images to process simultaneously.
    Returns list of results for each processed image.
    """
    image_dir = pathlib.Path(folder_path)
    if not image_dir.is_dir():
        print(f"Error: The specified folder '{folder_path}' does not exist.")
        return []

    image_extensions = ['.jpg']
    image_files = sorted([
        file for file in image_dir.iterdir() 
        if file.suffix.lower() in image_extensions
    ])

    if not image_files:
        print(f"No images found in the folder: {folder_path}")
        return []

    print(f"Found {len(image_files)} image(s) to process.\n")
    
    history = await load_history()
    # metadata = await load_metadata()
    logs = await load_log()
    
    if logs:
        print("Existing logs found. Resuming from last state.")
    else:
        print("No existing logs found. Starting a new session.")

    # Create semaphore to limit concurrent processing
    semaphore = asyncio.Semaphore(max_concurrent)
    
    # Create tasks for all images
    tasks = [asyncio.create_task(process_single_image(history, image_path, prompt, output_folder, logs, semaphore)) for image_path in image_files]
    
    # Process with as_completed to handle errors and cancel on failure
    for future in asyncio.as_completed(tasks):
        try:
            result = await future
        except Exception as e:
            print(f"Error processing image: {e}")
            print("Processing stopped due to error.")
            # Cancel pending tasks
            remaining = [t for t in tasks if not t.done()]
            for t in remaining:
                t.cancel()
            # Wait for cancellations to complete, ignoring exceptions
            if remaining:
                await asyncio.gather(*remaining, return_exceptions=True)
            raise

    print("All images processed successfully!")

async def main():
    """Main async function with retry logic and API key rotation."""
    global GEMINI_API_KEY, rotated_key

    os.environ["GEMINI_API_KEY"] = GEMINI_API_KEY
    print("GEMINI_API_KEY set to: ", GEMINI_API_KEY)
    output_dir = OUTPUT_FOLDER_PATH

    # Ensure output_dir is a Path object
    if isinstance(output_dir, str):
        output_dir = pathlib.Path(output_dir)

    max_main_retries = 40  # Maximum number of times to retry for rate limit errors
    main_retry_count = 0

    while main_retry_count < max_main_retries:
        try:
            # Check if all images are already processed
            if check_if_all_images_processed():
                print("All images have been processed successfully!")
                break
                
            await process_all_images_in_folder(IMAGE_FOLDER_PATH, PROMPT, output_dir, SYSTEM_PROMPT)
            print("All images processed successfully!")
            break  # Exit the retry loop if successful
            
        except Exception as e:
            error_str = str(e)
            print(f"Main function attempt {main_retry_count + 1}/{max_main_retries} failed: {e}")
            
            # Check if it's a rate limit error
            if "litellm.RateLimitError" in error_str or "Assistant response is None" in error_str:
                print("Rate limit or None response error detected. Rotating API key...")
                rotate_api_key()
                os.environ["GEMINI_API_KEY"] = GEMINI_API_KEY
                print(f"Switched to API key index: {current_api_key_index}")

                global rotated_key 
                rotated_key = True

                # Wait a bit before retrying with new API key
                await asyncio.sleep(5)
                
                main_retry_count += 1
                if main_retry_count < max_main_retries:
                    print(f"Retrying main function... (attempt {main_retry_count + 1})")
            else:
                print("Other error occurred. Stopping without retry.",e)
                break 
                # raise e
    
    print("Processing complete.")

if __name__ == "__main__":
    asyncio.run(main())