event {}

http{server {
    listen 80 default_server;

    client_max_body_size 100M;

    location /product/ {
       proxy_pass http://product:3000/;
    }

    location /product/master/ {
       proxy_pass http://product-master:4000/;
    }

    location /service/ {
       proxy_pass http://service-master:5000/;
    }

    location /ticket/ {
       proxy_pass http://service-ticket:6000/;
    }

    location /authentication/ {
       proxy_pass http://service-authentication:7000/;
    }


    #location / {
      #proxy_pass http://ui:80/;
      #proxy_http_version 1.1;
      #proxy_set_header Upgrade $http_upgrade;
      #proxy_set_header Connection 'upgrade';
      #proxy_set_header Host $host;
      #proxy_set_header X-Real-IP $remote_addr;
     # proxy_cache_bypass $http_upgrade;
    #}
    location / {
       root /usr/share/nginx/html;
       index index.html;
       try_files $uri /index.html;
    }

}
}