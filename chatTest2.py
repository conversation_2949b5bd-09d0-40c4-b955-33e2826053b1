import os
import pathlib
import google.generativeai as genai
from PIL import Image
import time
from datetime import datetime
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# --- Configuration ---

# 1. Set your API Key here.
# It's recommended to use environment variables or a secret manager for security.
# For example, uncomment the line below if you have set it as an environment variable.
# GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
GOOGLE_API_KEY = 'AIzaSyDQXU6UP3qJ5DBY0oFQi8vL5in2K8SV2oo' # <-- PASTE YOUR API KEY HERE

# 2. Set the path to the folder containing your images.
IMAGE_FOLDER_PATH = r'E:\loveable_AI\bunch\finalFrames_20250818_164828_copy'

# 3. The prompt you provided.
PROMPT = """Given a screenshot (frame), analyze and extract structured information with the following rules:

1. **Classify the frame into two regions:**
    - `browser_component`: Contains only the active tab title and the URL from the address bar.
    - `webpage`: The rendered website content inside the browser viewport.

2. **From the browser_component:**
    - Extract the **title of the active tab**.
    - Extract the **URL** visible in the address bar.

3. **From the webpage:**
    - Identify and classify visible subcomponents.

Return the analysis in valid YAML format only."""

# --- Code ---

def configure_genai():
    """Configures the Generative AI client with the API key."""
    try:
        genai.configure(api_key=GOOGLE_API_KEY)
    except Exception as e:
        print(f"Error configuring GenAI: {e}")
        print("Please ensure you have set a valid GOOGLE_API_KEY.")
        exit()
        
def setup_output_folder():
    """Creates a new timestamped directory for the output YAML files."""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir_name = f"today_output_{timestamp}"
    output_path = pathlib.Path(output_dir_name)
    output_path.mkdir(parents=True, exist_ok=True)
    print(f"Created output directory: {output_path}")
    return output_path


def generate_yaml_from_image(image_path, prompt_text, max_retries=4):
    """
    Analyzes a single image using the Gemini Pro Vision model with a retry mechanism.
    Uses chat.send_message for the request.

    Args:
        image_path (str): The path to the image file.
        prompt_text (str): The prompt to guide the model's analysis.
        max_retries (int): The maximum number of times to retry on failure.

    Returns:
        str: The generated YAML data as a string, or an error message.
    """
    print(f"-> Processing image: {image_path.name}")
    retries = 0
    while retries < max_retries:
        try:
            # Initialize the vision model
            model = genai.GenerativeModel(
                'gemini-2.5-pro',
                    safety_settings={
                    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
                    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                })
            
            # Start a chat session
            chat = model.start_chat(history=[])
            
            # Open the image
            img = Image.open(image_path)
            
            # Send the prompt and image as a single message
            response = chat.send_message([prompt_text, img])
            
            # Clean up the response to ensure it's valid YAML
            cleaned_response = response.text.strip()
            if cleaned_response.startswith("```yaml"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]
                
            return cleaned_response.strip()
            
        except FileNotFoundError:
            return f"Error: Image file not found at {image_path}"
        except Exception as e:
            retries += 1
            print(f"Attempt {retries}/{max_retries} failed for {image_path.name}. Error: {e}")
            if retries < max_retries:
                # Wait before retrying to avoid rate-limiting
                wait_time = 2 ** retries  # Exponential backoff
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                print(f"All {max_retries} attempts failed. Halting processing for this file.")
                return f"All attempts failed after {max_retries} retries."

def process_all_images_in_folder(folder_path, prompt, output_folder):
    """
    Iterates through all images in a folder, generates YAML, and prints and writes it.

    Args:
        folder_path (str): Path to the folder with images.
        prompt (str): The prompt to use for analysis.
        output_folder (pathlib.Path): The path to the output directory.
    """
    image_dir = pathlib.Path(folder_path)

    if not image_dir.is_dir():
        print(f"Error: The specified folder '{folder_path}' does not exist.")
        return

    # Supported image file extensions
    image_extensions = ['.png', '.jpg', '.jpeg', '.webp', '.heic']
    
    image_files = [
        file for file in image_dir.iterdir() 
        if file.suffix.lower() in image_extensions
    ]

    if not image_files:
        print(f"No images found in the folder: {folder_path}")
        return

    print(f"Found {len(image_files)} image(s) to process.\n")

    for image_path in image_files:
        yaml_output = generate_yaml_from_image(image_path, prompt)
        
        # Define the output file path
        output_filename = image_path.stem + '.yaml'
        output_file_path = output_folder / output_filename

        # Write the YAML output to a file
        with open(output_file_path, 'w') as f:
            f.write(yaml_output)

        # Print the results for each image
        print("-" * 50)
        print(f"File: {image_path.name}")
        print(f"Generated YAML written to: {output_file_path}")
        print("Generated YAML:")
        print(yaml_output)
        print("-" * 50 + "\n")

if __name__ == "__main__":
    # Configure the API client
    configure_genai()
    
    # Setup the output folder
    output_dir = setup_output_folder()

    # Start the processing
    process_all_images_in_folder(IMAGE_FOLDER_PATH, PROMPT, output_dir)
    
    print("Processing complete.")